# 日常审批系统

## 项目简介

日常审批系统是为教育局开发的一个综合性办公自动化平台，主要实现了出差申请、请假申请、车辆申请、会议申请和办公用品申请等五种审批流程的电子化管理。系统采用前后端分离架构，后端基于Java技术栈，前端使用Vue3框架，集成Camunda流程引擎实现标准化的工作流管理，支持流程自定义配置和可视化展示。

## 功能特点

- **统一审批平台**：集成5种常用审批流程，统一入口和操作方式
- **灵活流程配置**：支持自定义审批流程、节点和表单
- **智能流转规则**：基于申请内容和组织结构智能确定审批路径
- **全程可追溯**：审批过程全程留痕，支持随时查询流程状态
- **统计分析功能**：提供多维度统计图表，辅助管理决策
- **移动端响应式**：支持PC端和移动端访问，随时随地处理审批

## 系统架构

### 技术栈

#### 后端

- 开发语言：Java 11+
- 框架：Spring Boot 2.7.x
- ORM框架：MyBatis-Plus
- 数据库：MySQL 8.0
- 缓存：Redis
- 认证授权：Spring Security + JWT
- API文档：Swagger/Knife4j

#### 前端

- 框架：Vue 3
- 构建工具：Vite
- UI组件库：Element Plus
- 状态管理：Pinia
- 路由：Vue Router
- HTTP客户端：Axios
- 表单验证：VeeValidate

### 架构图

```
┌───────────────────────────────────────────────────────────────┐
│                           前端应用层                            │
│                                                               │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────┐ │
│  │  用户管理  │ │  审批管理 │ │ 流程配置  │ │ 统计分析  │ │ 系统 │ │
│  └──────────┘ └──────────┘ └──────────┘ └──────────┘ └──────┘ │
└───────────────────────────┬───────────────────────────────────┘
                            │
                            │ HTTP/JSON
                            ▼
┌───────────────────────────────────────────────────────────────┐
│                           后端服务层                            │
│                                                               │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────┐ │
│  │  用户服务  │ │  审批服务 │ │ 流程服务  │ │ 统计服务  │ │ 系统 │ │
│  └──────────┘ └──────────┘ └──────────┘ └──────────┘ └──────┘ │
└───────────────────────────┬───────────────────────────────────┘
                            │
                            ▼
┌───────────────────────────────────────────────────────────────┐
│                           数据持久层                            │
│                                                               │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐          │
│  │   MySQL   │ │   Redis  │ │  文件存储  │ │   日志    │          │
│  └──────────┘ └──────────┘ └──────────┘ └──────────┘          │
└───────────────────────────────────────────────────────────────┘
```

## 项目结构

```
pyjyj/
├── docs/                          # 项目文档
│   ├── design/                    # 设计文档
│   │   ├── 系统架构设计.md           # 系统架构设计
│   │   ├── 数据库设计.md             # 数据库设计
│   │   ├── 流程设计.md              # 流程设计
│   │   ├── API设计.md              # API接口设计
│   │   ├── 前端设计.md              # 前端UI设计
│   │   └── 实施方案.md              # 项目实施方案
│   └── database/                  # 数据库脚本
├── src/
│   ├── backend/                   # 后端代码
│   │   ├── pyjyj-admin/          # 管理后台接口
│   │   ├── pyjyj-framework/      # 框架核心模块
│   │   ├── pyjyj-system/         # 系统管理模块
│   │   ├── pyjyj-workflow/       # 工作流模块
│   │   └── pyjyj-common/         # 公共模块
│   └── frontend/                  # 前端代码
│       ├── public/                # 静态资源
│       ├── src/                   # 源代码
└── README.md                      # 项目说明
```

## 流程设计

系统实现了五种审批流程，每种流程的设计如下：

1. **出差申请流程**：
   - 申请人提交 → 部门负责人审批 → 条件判断(出差天数) → 分管副局长/办公室主任审批 → 党组书记审批 → 结束

2. **请假申请流程**：
   - 申请人提交 → 部门负责人审批 → 条件判断(请假天数) → 办公室主任/分管副局长审批 → 条件判断(是否>=7天) → 党组书记审批(可选) → 结束

3. **车辆申请流程**：
   - 申请人提交 → 部门负责人审批 → 办公室主任审批 → 结束

4. **会议申请流程**：
   - 申请人提交 → 部门负责人审批 → 条件判断(会议类型) → 分管副局长审批(可选) → 办公室主任审批 → 结束

5. **办公用品申请流程**：
   - 申请人提交 → 部门负责人审批 → 条件判断(申请金额) → 分管副局长审批(可选) → 办公室主任审批 → 结束

## 快速开始

### 环境要求

- JDK 11+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- Node.js 16+
- npm 8+

### 后端启动

1. 初始化数据库
   ```bash
   # 创建数据库
   create database pyjyj character set utf8mb4 collate utf8mb4_general_ci;

   # 导入SQL脚本
   mysql -u username -p pyjyj < docs/database/pyjyj.sql
   ```

2. 修改配置
   ```bash
   # 编辑后端配置文件
   vim src/backend/pyjyj-admin/src/main/resources/application.yml

   # 修改数据库连接信息和Redis配置
   ```

3. 编译运行
   ```bash
   cd src/backend
   mvn clean package -DskipTests
   java -jar pyjyj-admin/target/pyjyj-admin.jar
   ```

### 前端启动

1. 安装依赖
   ```bash
   cd src/frontend
   npm install
   ```

2. 修改配置
   ```bash
   # 编辑API配置
   vim src/frontend/.env.development

   # 修改API基础路径
   VITE_APP_BASE_API = 'http://localhost:8080/api'
   ```

3. 开发模式启动
   ```bash
   npm run dev
   ```

4. 生产模式构建
   ```bash
   npm run build
   ```

## 系统截图

(系统主要页面截图展示，正式项目中添加)

## 部署说明

### 后端部署

1. 准备环境：JDK 11+、MySQL 8.0+、Redis 6.0+
2. 创建数据库并导入SQL脚本
3. 修改配置文件中的数据库连接信息、Redis配置等
4. 打包项目生成jar文件
5. 使用nohup或systemd等方式启动服务

### 前端部署

1. 执行`npm run build`生成静态文件
2. 将dist目录下的文件部署到Web服务器(如Nginx)
3. 配置Nginx反向代理API请求

### Nginx配置示例

```nginx
server {
    listen 80;
    server_name pyjyj.example.com;

    # 前端静态资源
    location / {
        root /var/www/pyjyj/dist;
        try_files $uri $uri/ /index.html;
        index index.html;
    }

    # API反向代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 开发指南

### 添加新的审批流程

1. 在数据库中创建新的业务表单表
2. 添加对应的实体类、DTO、Mapper、Service和Controller
3. 在流程定义表中添加新的流程记录
4. 配置流程节点
5. 前端添加对应的表单组件和路由

### 自定义表单字段

1. 修改表单定义表中的表单JSON结构
2. 前端实现对应的动态表单渲染组件

## 维护与支持

### 问题排查

1. 数据库连接问题：检查数据库配置、网络连接和权限设置
2. Redis连接问题：检查Redis服务状态和连接配置
3. API请求失败：检查网络环境、跨域配置和请求参数
4. 权限不足：检查用户角色和权限配置

### 日志查看

```bash
# 查看后端日志
tail -f logs/pyjyj-admin.log

# 查看Nginx访问日志
tail -f /var/log/nginx/access.log

# 查看Nginx错误日志
tail -f /var/log/nginx/error.log
```

## 技术文档

- [系统架构设计](./docs/design/系统架构设计.md)
- [数据库设计](./docs/design/数据库设计.md)
- [流程设计](./docs/design/流程设计.md)
- [API设计](./docs/design/API设计.md)
- [前端设计](./docs/design/前端设计.md)
- [实施方案](./docs/design/实施方案.md)

## 许可证

本项目采用 [MIT 许可证](LICENSE)。