# 前端设计

## 概述

本文档描述了日常审批系统的前端设计方案，包括整体架构、技术栈选择、页面布局和核心功能模块的 UI 设计。前端采用 Vue 3 框架和 Element Plus 组件库，实现响应式布局，确保在不同设备上都有良好的用户体验。

## 技术栈

- **框架**：Vue 3
- **构建工具**：Vite
- **UI 组件库**：Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router
- **HTTP 客户端**：Axios
- **CSS 预处理**：SCSS
- **图表库**：ECharts
- **工具库**：
  - Lodash（工具函数）
  - Day.js（日期处理）
  - VeeValidate（表单验证）

## 目录结构

```
src/
├── api/                # API接口
│   ├── auth.js         # 认证接口
│   ├── user.js         # 用户管理接口
│   ├── workflow.js     # 工作流接口
│   └── business/       # 业务表单接口
├── assets/             # 静态资源
│   ├── images/         # 图片资源
│   ├── icons/          # 图标资源
│   └── styles/         # 样式资源
├── components/         # 通用组件
│   ├── common/         # 公共组件
│   ├── form/           # 表单组件
│   ├── table/          # 表格组件
│   └── chart/          # 图表组件
├── composables/        # 组合式API
├── config/             # 配置文件
├── directives/         # 自定义指令
├── layout/             # 布局组件
│   ├── components/     # 布局子组件
│   └── index.vue       # 主布局
├── router/             # 路由配置
│   ├── routes/         # 路由模块
│   └── index.js        # 路由入口
├── stores/             # 状态管理
│   ├── modules/        # 状态模块
│   └── index.js        # 状态入口
├── utils/              # 工具函数
│   ├── request.js      # HTTP请求封装
│   ├── auth.js         # 认证工具
│   └── formatter.js    # 格式化工具
├── views/              # 页面组件
│   ├── auth/           # 认证页面
│   ├── dashboard/      # 首页仪表盘
│   ├── system/         # 系统管理
│   ├── workflow/       # 流程管理
│   └── business/       # 业务表单
├── App.vue             # 根组件
├── .env                # 项目环境变量
└── main.js             # 入口文件
```

## 整体布局设计

系统采用经典的管理后台布局，分为四个主要区域：

1. **顶部导航栏（Header）**：包含系统 logo、折叠菜单按钮、面包屑导航、用户信息、通知中心、全屏按钮等
2. **侧边菜单栏（Sidebar）**：显示系统功能模块，支持折叠/展开
3. **主内容区（Main Content）**：显示当前功能模块的内容
4. **页脚（Footer）**：显示版权信息和系统版本

布局示意图：

```
+----------------------------------------------------------+
|                        Header                            |
+-------------+----------------------------------------------+
|             |                                            |
|             |                                            |
|   Sidebar   |                                            |
|             |                                            |
|             |               Main Content                 |
|             |                                            |
|             |                                            |
|             |                                            |
+-------------+----------------------------------------------+
|                        Footer                            |
+----------------------------------------------------------+
```

### 响应式设计

- 大屏（>1200px）：完整显示侧边栏和内容区
- 中屏（768px-1200px）：自动折叠侧边栏，点击可展开
- 小屏（<768px）：隐藏侧边栏，通过弹出菜单访问

## 主题与样式设计

### 色彩系统

- **主题色**：#1890ff（蓝色）
- **辅助色**：
  - 成功：#52c41a（绿色）
  - 警告：#faad14（黄色）
  - 错误：#f5222d（红色）
  - 信息：#1890ff（蓝色）
- **中性色**：
  - 标题：#262626
  - 正文：#595959
  - 次要文本：#8c8c8c
  - 边框：#d9d9d9
  - 分割线：#e8e8e8
  - 背景：#f5f5f5
  - 表格头背景：#fafafa

### 字体

- 默认字体：
  - 中文：Microsoft YaHei, PingFang SC
  - 英文：-apple-system, BlinkMacSystemFont, Segoe UI
- 代码字体：Source Code Pro, Consolas, monospace
- 字号：
  - 主标题：20px
  - 次级标题：18px
  - 小标题：16px
  - 正文：14px
  - 辅助文字：12px

### 间距

采用 8px 倍数作为基础间距单位：

- 紧凑：8px
- 常规：16px
- 宽松：24px
- 超宽：32px

## 核心页面设计

### 1. 登录页面

![登录页面](示意图路径)

- **布局**：居中的登录表单，背景可使用渐变色或简洁背景图
- **元素**：
  - 系统 Logo 和名称
  - 用户名输入框
  - 密码输入框
  - 记住密码选项
  - 登录按钮
  - 版权信息

### 2. 首页仪表盘

![首页仪表盘](示意图路径)

- **布局**：多卡片式布局，顶部统计数据，下方图表展示
- **元素**：
  - 统计卡片（待办任务、已办任务、我的申请、通过率）
  - 最近审批流程图表（折线图）
  - 审批分类统计（饼图）
  - 待办任务列表
  - 通知公告

### 3. 系统管理

#### 3.1 用户管理

![用户管理](示意图路径)

- **布局**：上方搜索栏和操作按钮，下方表格展示
- **元素**：
  - 搜索条件（用户名、部门、状态等）
  - 功能按钮（新增、批量删除等）
  - 用户列表表格
  - 分页控件
  - 操作菜单（编辑、删除、重置密码等）

#### 3.2 角色管理

![角色管理](示意图路径)

- **布局**：左侧角色列表，右侧权限分配
- **元素**：
  - 角色列表
  - 角色信息表单
  - 权限树形选择器
  - 已授权用户列表

#### 3.3 部门管理

![部门管理](示意图路径)

- **布局**：左侧部门树，右侧部门信息
- **元素**：
  - 部门树形结构
  - 部门信息表单
  - 部门人员列表

### 4. 审批管理

#### 4.1 我的申请

![我的申请](示意图路径)

- **布局**：上方搜索栏和申请按钮，下方表格展示
- **元素**：
  - 搜索条件（申请类型、状态、时间范围等）
  - 发起申请按钮
  - 申请列表表格
  - 分页控件
  - 操作菜单（查看、撤回、修改等）

#### 4.2 我的待办

![我的待办](示意图路径)

- **布局**：上方搜索栏，下方表格展示
- **元素**：
  - 搜索条件（申请类型、申请人、时间范围等）
  - 待办任务列表表格
  - 分页控件
  - 操作按钮（审批、转交等）

#### 4.3 我的已办

![我的已办](示意图路径)

- **布局**：与我的待办类似
- **元素**：
  - 搜索条件
  - 已办任务列表表格
  - 分页控件
  - 查看详情按钮

#### 4.4 申请详情/审批页面

![申请详情](示意图路径)

- **布局**：上方流程信息和流程图，中间表单展示，下方审批历史和审批操作
- **元素**：
  - 流程基本信息（标题、申请人、状态等）
  - 流程流转图
  - 申请表单（只读/可编辑）
  - 附件列表
  - 审批历史记录
  - 审批操作区（同意、拒绝、退回、转交等）
  - 审批意见输入框

### 5. 流程管理

#### 5.1 流程定义

![流程定义](示意图路径)

- **布局**：上方搜索栏和操作按钮，下方表格展示
- **元素**：
  - 搜索条件（流程名称、类型、状态等）
  - 功能按钮（新增、导入、导出等）
  - 流程列表表格
  - 分页控件
  - 操作菜单（编辑、删除、发布等）

#### 5.2 流程设计器

![流程设计器](示意图路径)

- **布局**：左侧节点工具箱，中间设计画布，右侧属性面板
- **元素**：
  - 节点工具箱（开始、审批、条件、结束等节点）
  - 设计画布（拖拽式流程设计）
  - 节点连线工具
  - 节点属性面板
  - 保存、发布按钮

### 6. 业务表单

#### 6.1 表单选择页面

![表单选择](示意图路径)

- **布局**：表单类型卡片网格布局
- **元素**：
  - 各类申请表单卡片
  - 每个卡片包含图标、标题和简短描述

#### 6.2 出差申请表单

![出差申请表单](示意图路径)

- **布局**：分组式表单布局
- **元素**：
  - 基本信息组（标题、申请人、部门等）
  - 出差信息组（出差类型、起止时间、天数、目的地等）
  - 出差事由
  - 同行人员
  - 交通方式和预计费用
  - 附件上传
  - 备注
  - 保存草稿/提交按钮

#### 6.3 请假申请表单

![请假申请表单](示意图路径)

- **布局**：与出差表单类似
- **元素**：
  - 基本信息组
  - 请假信息组（请假类型、起止时间、天数）
  - 请假事由
  - 紧急联系方式
  - 工作交接人
  - 附件上传
  - 保存草稿/提交按钮

#### 6.4 车辆申请表单

![车辆申请表单](示意图路径)

- **元素**：
  - 基本信息组
  - 用车信息组（用车时间、目的地、用车事由）
  - 乘车人员
  - 司机信息
  - 预计里程
  - 保存草稿/提交按钮

#### 6.5 会议申请表单

![会议申请表单](示意图路径)

- **元素**：
  - 基本信息组
  - 会议信息组（会议类型、会议室、起止时间）
  - 参会人员
  - 会议主题和内容
  - 设备需求
  - 餐饮需求
  - 附件上传
  - 保存草稿/提交按钮

#### 6.6 办公用品申请表单

![办公用品申请表单](示意图路径)

- **元素**：
  - 基本信息组
  - 申请信息组（申请日期、用途）
  - 物品明细表格（支持动态添加行）
  - 总金额（自动计算）
  - 附件上传
  - 保存草稿/提交按钮

### 7. 统计分析

#### 7.1 审批统计

![审批统计](示意图路径)

- **布局**：上方筛选条件，下方图表和数据
- **元素**：
  - 筛选条件（时间范围、流程类型等）
  - 总体统计卡片
  - 流程类型分布图（饼图）
  - 月度审批趋势图（折线图）
  - 审批效率分析（柱状图）

#### 7.2 个人统计

![个人统计](示意图路径)

- **布局**：与审批统计类似
- **元素**：
  - 我的申请统计
  - 我的审批统计
  - 申请通过率
  - 审批平均耗时

## 交互设计

### 表单交互

1. **表单验证**：输入时实时验证，提交时全面验证
2. **动态表单**：根据选择动态显示/隐藏表单项
3. **表单分步**：复杂表单采用分步填写方式
4. **自动计算**：数字字段自动计算（如总金额、天数等）
5. **草稿保存**：自动/手动保存草稿功能

### 表格交互

1. **排序**：支持点击表头排序
2. **筛选**：支持列筛选
3. **展开/收起**：支持行展开显示详情
4. **拖拽调整**：支持列宽调整和列顺序拖拽
5. **批量操作**：支持行选择和批量操作

### 流程交互

1. **流程图展示**：可视化当前流程位置
2. **审批操作**：提供便捷的审批操作按钮
3. **意见模板**：提供常用审批意见模板
4. **流转提示**：流程流转后及时提示

## 组件设计

### 1. 通用组件

- **SearchForm**：通用搜索表单组件
- **DataTable**：通用数据表格组件
- **Pagination**：通用分页组件
- **FileUpload**：文件上传组件
- **IconSelect**：图标选择组件
- **StatusTag**：状态标签组件
- **DialogForm**：弹窗表单组件

### 2. 业务组件

- **ProcessDesigner**：流程设计器组件
- **ProcessViewer**：流程查看器组件
- **ApprovalHistory**：审批历史组件
- **ApprovalActions**：审批操作组件
- **DynamicForm**：动态表单组件
- **UserSelector**：用户选择组件
- **DeptSelector**：部门选择组件

## 性能优化策略

1. **路由懒加载**：按需加载路由组件
2. **组件按需导入**：Element Plus 组件按需导入
3. **虚拟滚动**：长列表使用虚拟滚动
4. **图片懒加载**：非可视区域图片延迟加载
5. **代码分割**：将代码分割成小块
6. **缓存策略**：合理使用浏览器缓存和应用缓存
7. **状态管理优化**：避免不必要的状态更新

## 兼容性

系统支持以下浏览器：

- Chrome 80+
- Firefox 78+
- Safari 13+
- Edge 80+
- IE：不支持

## 无障碍设计

1. **语义化标签**：使用合适的 HTML5 语义化标签
2. **焦点管理**：确保键盘可以导航全部交互元素
3. **图像替代文本**：为所有图像提供 alt 文本
4. **颜色对比度**：确保文本和背景有足够对比度
5. **表单标签**：为所有表单控件提供适当的标签

## 国际化支持

系统设计预留国际化支持：

1. 文本使用 i18n 配置文件管理
2. 日期格式根据地区自动调整
3. 数字格式根据地区自动调整

## 安全考虑

1. **XSS 防护**：输入输出过滤
2. **CSRF 防护**：使用 CSRF Token
3. **权限控制**：前端路由和组件级权限控制

## 后续迭代计划

1. **移动端适配**：优化移动端体验
2. **主题定制**：支持用户自定义主题
3. **工作台个性化**：支持用户自定义首页布局
4. **离线功能**：支持部分功能离线使用
5. **流程可视化增强**：更丰富的流程图展示
