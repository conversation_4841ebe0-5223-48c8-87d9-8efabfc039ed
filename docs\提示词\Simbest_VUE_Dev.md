---
description: 
globs: 
alwaysApply: false
---
---
description: 创建前端vue3项目时，或明确使用vue3框架项目时，引用此规则，并在后续的编码时引用
globs: 
alwaysApply: false
---
# Vue 3 开发助手提示词

## 技术栈概览

- **核心框架**：
  - Vue 3 (组合式 API)
  - Vite (构建工具)
  - TypeScript (类型系统)
  - Pinia (状态管理)
  - Vue Router (路由管理)
  - Axios (HTTP 客户端)

- **UI 与可视化**：
  - Element Plus (UI 组件库)
  - ECharts (数据可视化)

## 项目结构指南

```
src/
├── api/                 # API 请求模块
├── assets/              # 静态资源
├── components/          # 通用组件
├── composables/         # 组合式函数
├── directives/          # 自定义指令
├── layouts/             # 布局组件
├── router/              # 路由配置
├── stores/              # Pinia 状态管理
├── types/               # TypeScript 类型定义
├── utils/               # 工具函数
├── views/               # 页面视图组件
├── App.vue              # 根组件
└── main.ts              # 应用入口
```

## 编码规范与最佳实践

### 组件命名

- 单文件组件使用 PascalCase 命名（如 `UserProfile.vue`）
- 基础组件使用 `Base` 前缀（如 `BaseButton.vue`）
- 单例组件使用 `The` 前缀（如 `TheHeader.vue`）
- 紧密耦合的组件使用父组件名称作为前缀（如 `UserProfileAvatar.vue`）

### TypeScript 集成

- 为所有 props、emits 和函数参数定义类型
- 为组件、store 和 API 响应创建接口
- 使用 `defineProps<{...}>()` 和 `defineEmits<{...}>()` 进行类型定义
- 为复杂对象创建类型别名或接口

### Vue 3 组件

```typescript
<script setup lang="ts">
// 导入
import { ref, computed, onMounted } from 'vue'
import type { PropType } from 'vue'

// Props 定义
const props = defineProps<{
  items: Array<Item>
  title: string
  isLoading?: boolean
}>()

// Emits 定义
const emit = defineEmits<{
  (e: 'update', value: string): void
  (e: 'delete', id: number): void
}>()

// 响应式状态
const count = ref(0)
const name = ref('')

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 生命周期钩子
onMounted(() => {
  console.log('Component mounted')
})

// 方法
const increment = () => {
  count.value++
  emit('update', count.value.toString())
}
</script>

<template>
  <div class="component">
    <h1>{{ title }}</h1>
    <p>Count: {{ count }}</p>
    <button @click="increment">Increment</button>
  </div>
</template>

<style scoped>
.component {
  /* 样式 */
}
</style>
```

## Pinia 状态管理

### Store 定义

```typescript
// stores/counter.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useCounterStore = defineStore('counter', () => {
  // 状态
  const count = ref(0)
  const name = ref('Counter')

  // 计算属性
  const doubleCount = computed(() => count.value * 2)

  // 动作
  function increment() {
    count.value++
  }

  function reset() {
    count.value = 0
  }

  return { count, name, doubleCount, increment, reset }
})
```

### 在组件中使用 Store

```typescript
<script setup lang="ts">
import { useCounterStore } from '@/stores/counter'
import { storeToRefs } from 'pinia'

const store = useCounterStore()

// 解构时保持响应性
const { count, doubleCount } = storeToRefs(store)
// 动作不需要通过 storeToRefs
const { increment } = store
</script>

<template>
  <div>
    <p>Count: {{ count }}</p>
    <p>Double: {{ doubleCount }}</p>
    <button @click="increment">Increment</button>
    <button @click="store.reset">Reset</button>
  </div>
</template>
```

## Vue Router 配置

### 路由设置

```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import HomeView from '@/views/HomeView.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'home',
    component: HomeView
  },
  {
    path: '/about',
    name: 'about',
    // 路由懒加载
    component: () => import('@/views/AboutView.vue')
  },
  {
    path: '/user/:id',
    name: 'user',
    component: () => import('@/views/UserView.vue'),
    props: true,
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 全局导航守卫
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login')
  } else {
    next()
  }
})

export default router
```

### 在组件中使用路由

```typescript
<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 获取路由参数
const userId = route.params.id

// 编程式导航
const goToHome = () => {
  router.push('/')
}

const goToUser = (id: string) => {
  router.push({
    name: 'user',
    params: { id }
  })
}
</script>
```

## Axios 配置与使用

### API 封装

```typescript
// api/http.ts
import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

const baseURL = import.meta.env.VITE_API_BASE_URL || '/api'

const http: AxiosInstance = axios.create({
  baseURL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response) {
      // 处理特定状态码
      switch (error.response.status) {
        case 401:
          // 处理未授权
          break
        case 404:
          // 处理未找到
          break
        case 500:
          // 处理服务器错误
          break
      }
    }
    return Promise.reject(error)
  }
)

export default http
```

### API 模块

```typescript
// api/users.ts
import http from './http'
import type { User, UserCreateData, UserUpdateData } from '@/types'

export const userApi = {
  getUsers: () => http.get<User[]>('/users'),
  
  getUser: (id: number) => http.get<User>(`/users/${id}`),
  
  createUser: (data: UserCreateData) => http.post<User>('/users', data),
  
  updateUser: (id: number, data: UserUpdateData) => http.put<User>(`/users/${id}`, data),
  
  deleteUser: (id: number) => http.delete(`/users/${id}`)
}
```

### 在组件中使用 API

```typescript
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { userApi } from '@/api/users'
import type { User } from '@/types'

const users = ref<User[]>([])
const loading = ref(true)
const error = ref<string | null>(null)

const fetchUsers = async () => {
  loading.value = true
  error.value = null
  
  try {
    users.value = await userApi.getUsers()
  } catch (err) {
    error.value = '获取用户数据失败'
    console.error(err)
  } finally {
    loading.value = false
  }
}

onMounted(fetchUsers)
</script>
```

## Element Plus 使用

### 引入与配置

```typescript
// main.ts
import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// 可选的中文本地化
import zhCn from 'element-plus/es/locale/lang/zh-cn'

const app = createApp(App)
app.use(ElementPlus, {
  locale: zhCn
})
app.mount('#app')
```

### 表单示例

```vue
<template>
  <el-form 
    ref="formRef" 
    :model="form" 
    :rules="rules" 
    label-width="120px"
  >
    <el-form-item label="用户名" prop="username">
      <el-input v-model="form.username" />
    </el-form-item>
    
    <el-form-item label="邮箱" prop="email">
      <el-input v-model="form.email" />
    </el-form-item>
    
    <el-form-item label="角色" prop="role">
      <el-select v-model="form.role" placeholder="请选择角色">
        <el-option
          v-for="role in roles"
          :key="role.value"
          :label="role.label"
          :value="role.value"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

const formRef = ref<FormInstance>()

const form = reactive({
  username: '',
  email: '',
  role: ''
})

const rules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
})

const roles = [
  { value: 'admin', label: '管理员' },
  { value: 'editor', label: '编辑' },
  { value: 'user', label: '普通用户' }
]

const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      console.log('提交表单', form)
      // 执行表单提交逻辑
    } else {
      console.error('表单验证失败', fields)
    }
  })
}

const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
</script>
```

## ECharts 集成

### 基本设置

```vue
<template>
  <div ref="chartRef" style="width: 100%; height: 400px"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 图表选项
const chartOptions: EChartsOption = {
  title: {
    text: '销售数据'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['销售额', '利润']
  },
  xAxis: {
    type: 'category',
    data: ['一月', '二月', '三月', '四月', '五月', '六月', '七月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '销售额',
      type: 'line',
      data: [150, 230, 224, 218, 135, 147, 260]
    },
    {
      name: '利润',
      type: 'line',
      data: [80, 100, 121, 104, 75, 82, 150]
    }
  ]
}

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    chartInstance.setOption(chartOptions)
    
    // 窗口大小变化时自动调整图表大小
    window.addEventListener('resize', () => {
      chartInstance?.resize()
    })
  }
}

// 更新图表数据
const updateChartData = (data: any) => {
  if (chartInstance) {
    // 更新数据部分
    chartInstance.setOption({
      series: [
        {
          data: data.sales
        },
        {
          data: data.profit
        }
      ]
    })
  }
}

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  // 销毁图表实例，释放资源
  if (chartInstance) {
    window.removeEventListener('resize', () => {
      chartInstance?.resize()
    })
    chartInstance.dispose()
    chartInstance = null
  }
})

// 可以将这些方法暴露给父组件
defineExpose({
  updateChartData
})
</script>
```

## 组合式函数 (Composables)

### 分页逻辑

```typescript
// composables/usePagination.ts
import { ref, computed } from 'vue'

export function usePagination(initialPageSize = 10) {
  const currentPage = ref(1)
  const pageSize = ref(initialPageSize)
  const total = ref(0)
  
  const offset = computed(() => (currentPage.value - 1) * pageSize.value)
  
  const pageCount = computed(() => Math.ceil(total.value / pageSize.value))
  
  const setTotal = (count: number) => {
    total.value = count
  }
  
  const goToPage = (page: number) => {
    currentPage.value = page
  }
  
  const nextPage = () => {
    if (currentPage.value < pageCount.value) {
      currentPage.value++
    }
  }
  
  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value--
    }
  }
  
  const changePageSize = (size: number) => {
    pageSize.value = size
    currentPage.value = 1 // 重置到第一页
  }
  
  return {
    currentPage,
    pageSize,
    total,
    offset,
    pageCount,
    setTotal,
    goToPage,
    nextPage,
    prevPage,
    changePageSize
  }
}
```

### 表单处理

```typescript
// composables/useForm.ts
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'

export function useForm<T extends Record<string, any>>(initialData: T) {
  const formRef = ref<FormInstance>()
  const formData = reactive<T>({ ...initialData })
  const isSubmitting = ref(false)
  
  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    
    // 重置为初始值
    Object.keys(initialData).forEach(key => {
      formData[key as keyof T] = initialData[key as keyof T]
    })
  }
  
  const validateForm = async () => {
    if (!formRef.value) return false
    
    return await formRef.value.validate()
      .then(() => true)
      .catch(() => false)
  }
  
  return {
    formRef,
    formData,
    isSubmitting,
    resetForm,
    validateForm
  }
}
```

## 全局工具函数

```typescript
// utils/index.ts

// 日期格式化
export const formatDate = (date: Date | string, format = 'YYYY-MM-DD'): string => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  fn: T,
  delay = 300
): ((...args: Parameters<T>) => void) => {
  let timer: number | null = null
  
  return function(...args: Parameters<T>) {
    if (timer) clearTimeout(timer)
    timer = window.setTimeout(() => {
      fn(...args)
      timer = null
    }, delay)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  fn: T,
  limit = 300
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false
  
  return function(...args: Parameters<T>) {
    if (!inThrottle) {
      fn(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

// 金额格式化
export const formatCurrency = (
  amount: number,
  currency = 'CNY',
  locale = 'zh-CN'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency
  }).format(amount)
}
```

## TypeScript 类型定义

```typescript
// types/index.ts

// 通用分页响应
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 通用 API 响应
export interface ApiResponse<T> {
  data: T
  code: number
  message: string
  success: boolean
}

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  role: 'admin' | 'editor' | 'user'
  createdAt: string
  updatedAt: string
}

export type UserCreateData = Omit<User, 'id' | 'createdAt' | 'updatedAt'>
export type UserUpdateData = Partial<UserCreateData>

// 表格配置类型
export interface TableConfig {
  loading: boolean
  columns: TableColumn[]
  showSelection?: boolean
  showIndex?: boolean
  height?: string | number
  maxHeight?: string | number
}

export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  sortable?: boolean
  fixed?: boolean | 'left' | 'right'
  formatter?: (row: any, column: any, cellValue: any) => any
}
```

## Vite 配置

```typescript
// vite.config.ts
import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  
  return {
    plugins: [
      vue(),
      vueJsx(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
        imports: ['vue', 'vue-router', 'pinia'],
        dts: 'src/types/auto-imports.d.ts'
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        dts: 'src/types/components.d.ts'
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    server: {
      port: 3000,
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      cssCodeSplit: true,
      sourcemap: mode !== 'production',
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['vue', 'vue-router', 'pinia'],
            element: ['element-plus'],
            echarts: ['echarts']
          }
        }
      }
    }
  }
})
```

## 开发注意事项

1. **性能优化**：
   - 使用异步组件懒加载路由页面
   - 合理使用 `v-memo` 和 `v-once` 避免不必要的渲染
   - 使用 `shallowRef` 和 `shallowReactive` 处理大型对象
   - 对于大型列表考虑使用虚拟滚动

2. **响应式陷阱**：
   - 避免大型响应式对象导致性能问题
   - 解构 Store 时使用 `storeToRefs` 保持响应性
   - 组合式 API 中返回的计算属性和方法不会自动绑定 `this`
   
3. **TypeScript 技巧**：
   - 使用 `interface` 扩展现有类型
   - 为组件 `ref` 指定正确的类型以获得更好的自动完成
   - 使用泛型约束增强类型安全

4. **测试**：
   - 使用 Vitest 进行单元测试
   - 使用 Cypress 进行端到端测试
   - 为每个组件编写单元测试，确保其按预期工作