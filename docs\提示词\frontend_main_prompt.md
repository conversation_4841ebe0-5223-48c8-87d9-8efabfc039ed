---
description: 在每次会话开始时应用此提示，确保所有其他提示都被正确读取和应用
globs:
alwaysApply: true
---

# 提示优先级规则

## 目的

确保每次新建会话时，助手都会读取并理解本文件中提到的所有提示文件，并在每次回答中确认已遵循这些规则。

## 规则

1. 在每次新会话开始时，助手必须读取 `docs/提示词/Simbest_VUE_Dev` 和 `docs/技术文档/前端设计` 提示文件。

2. 助手必须确保完全理解并遵循这些提示文件中的所有规范和要求。

3. 在每次回答中，助手必须在回答的末尾列出所有已读取的提示文件，格式如下：

   ```
   ---
   已遵循的提示规范：
   - docs/simbest_vue_dev.mdc (Vue 3 开发规范)
   - [其他已读取的提示文件]
   ```

4. 助手必须明确表示当前回答是按照这些规则编写的，例如："本回答遵循了上述所有提示规范。"

5. 如果助手无法访问或理解某个提示文件，必须在列表中注明，例如："无法访问 [文件名]"。

6. 助手必须确保其回答内容符合所有已读取提示文件中的规范和要求。

7. 如果不同提示文件之间存在冲突，助手应优先遵循更具体的规范，并在回答中说明这一决定。

8. 指定前端工程目录为 frontend 目录，所有前端操作都在 frontend 目录下执行

## 工作流程

1. 读取 `docs/提示词/Simbest_VUE_Dev.md` 和 `docs/技术文档/前端设计.md` 文件，识别其中提到的所有提示文件。

2. 依次读取并理解每个提示文件的内容。

3. 根据提示文件中的规范和要求，生成符合要求的回答。

4. 在回答末尾列出所有已读取的提示文件，并确认已遵循这些规则。

5. 如果用户的请求与某些提示规范冲突，助手应说明冲突并建议符合规范的替代方案。

## 注意事项

- 这个提示规则的优先级高于其他所有提示规则。

- 即使用户没有明确要求，助手也必须在每次回答中列出已遵循的提示规范。

- 助手应该避免在回答中重复提示文件的全部内容，而是应该将其规范融入到回答中。

- 如果用户明确要求不遵循某些规范，助手应该说明这样做的潜在问题，但仍然可以按照用户的要求提供答案。

- 指定前端工程目录为 frontend 目录，所有前端操作都在 frontend 目录下执行
