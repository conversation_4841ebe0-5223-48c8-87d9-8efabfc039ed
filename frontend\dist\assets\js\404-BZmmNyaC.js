import{a as s}from"./index-cttjCPxy.js";import{e,i as r,j as a,k as o,w as t,a0 as c,D as i,u as n,o as d}from"./vue-element-BXsXg3SF.js";import"./utils-OxSuZc4o.js";const l={class:"error-container"},m={class:"error-content"},p=s(e({__name:"404",setup(s){const e=n(),goHome=()=>{e.push("/")};return(s,e)=>{const n=c;return d(),r("div",l,[a("div",m,[e[1]||(e[1]=a("h1",{class:"error-code"},"404",-1)),e[2]||(e[2]=a("div",{class:"error-message"},"页面未找到",-1)),e[3]||(e[3]=a("div",{class:"error-desc"},"抱歉，您访问的页面不存在或已被移除",-1)),o(n,{type:"primary",onClick:goHome},{default:t((()=>e[0]||(e[0]=[i("返回首页")]))),_:1})])])}}}),[["__scopeId","data-v-fe3bae24"]]);export{p as default};
