import{a}from"./index-cttjCPxy.js";import{e as t,f as e,a as s,al as l,c as o,o as r,w as m,i as p,F as u,t as n,aQ as h,j as d,z as c,aR as i}from"./vue-element-BXsXg3SF.js";import"./utils-OxSuZc4o.js";const f=a(t({__name:"Breadcrumb",setup(a){const t=e(),f=s([]),getBreadcrumb=()=>{let a=t.matched.filter((a=>a.meta&&a.meta.title));a.length>0&&"/dashboard"!==a[0].path&&(a=[{path:"/dashboard",meta:{title:"首页"}}].concat(a));const e=[];let s=!1;a.forEach((a=>{a.meta&&"首页"===a.meta.title?s||(e.push(a),s=!0):e.push(a)})),f.value=e};return getBreadcrumb(),l((()=>t.path),(()=>getBreadcrumb())),(a,t)=>{const e=h,s=i;return r(),o(s,{separator:"/"},{default:m((()=>[(r(!0),p(u,null,n(f.value,((a,t)=>(r(),o(e,{key:a.path,to:0===t?{path:a.path}:""},{default:m((()=>[d("span",null,c(a.meta&&a.meta.title),1)])),_:2},1032,["to"])))),128))])),_:1})}}}),[["__scopeId","data-v-80fe5a37"]]);export{f as default};
