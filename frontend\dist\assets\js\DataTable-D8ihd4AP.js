import{a as e}from"./index-cttjCPxy.js";/* empty css                      *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                 */import{e as a,a as t,b as l,i,k as n,a5 as o,w as r,ad as d,ai as s,am as p,aj as u,c as g,ab as c,ao as y,aK as f,aa as b,ak as h,o as m}from"./vue-element-BXsXg3SF.js";import"./OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js";const S={class:"data-table-container"},x={key:0,class:"table-toolbar"},w={key:1,class:"pagination-container"},v=e(a({__name:"DataTable",props:{data:{type:Array,required:!1,default:()=>[]},border:{type:Boolean,default:!0},stripe:{type:Boolean,default:!0},rowKey:{type:String,default:"id"},loading:{type:Boolean,default:!0},showSelection:{type:Boolean,default:!1},showIndex:{type:Boolean,default:!1},indexLabel:{type:String,default:"序号"},indexWidth:{type:[String,Number],default:60},indexAlign:{type:String,default:"center"},actionLabel:{type:String,default:"操作"},actionWidth:{type:[String,Number],default:180},actionFixed:{type:String,default:"right"},actionAlign:{type:String,default:"center"},showPagination:{type:Boolean,default:!0},currentPage:{type:Number,default:1},pageSize:{type:Number,default:10},pageSizes:{type:Array,default:()=>[10,20,50,100]},paginationLayout:{type:String,default:"total, sizes, prev, pager, next, jumper"},total:{type:Number,default:0},background:{type:Boolean,default:!0}},emits:["update:currentPage","update:pageSize","size-change","current-change","selection-change"],setup(e,{expose:a,emit:v}){const z=e,_=v,k=t(),j=l((()=>z.currentPage)),A=l((()=>z.pageSize)),handleSelectionChange=e=>{_("selection-change",e)},handleSizeChange=e=>{_("update:pageSize",e),_("size-change",e)},handleCurrentChange=e=>{_("update:currentPage",e),_("current-change",e)};return a({tableRef:k,clearSelection:()=>{var e;null==(e=k.value)||e.clearSelection()},toggleRowSelection:(e,a)=>{var t;null==(t=k.value)||t.toggleRowSelection(e,a)},toggleAllSelection:()=>{var e;null==(e=k.value)||e.toggleAllSelection()}}),(a,t)=>{const l=c,v=y,z=b,_=h,B=o,P=u;return m(),i("div",S,[n(B,{"body-style":{padding:"20px"}},{default:r((()=>[a.$slots.toolbar?(m(),i("div",x,[p(a.$slots,"toolbar",{},void 0,!0)])):d("",!0),s((m(),g(z,f({ref_key:"tableRef",ref:k},a.$attrs,{data:e.data,border:e.border,stripe:e.stripe,"row-key":e.rowKey,onSelectionChange:handleSelectionChange}),{empty:r((()=>[n(v,{description:"暂无数据"})])),default:r((()=>[e.showSelection?(m(),g(l,{key:0,type:"selection",width:"55",align:"center",fixed:"left"})):d("",!0),e.showIndex?(m(),g(l,{key:1,type:"index",label:e.indexLabel,width:e.indexWidth,align:e.indexAlign,fixed:"left"},null,8,["label","width","align"])):d("",!0),p(a.$slots,"default",{},void 0,!0),a.$slots.action?(m(),g(l,{key:2,label:e.actionLabel,width:e.actionWidth,fixed:e.actionFixed,align:e.actionAlign},{default:r((e=>[p(a.$slots,"action",{row:e.row,index:e.$index},void 0,!0)])),_:3},8,["label","width","fixed","align"])):d("",!0)])),_:3},16,["data","border","stripe","row-key"])),[[P,e.loading]]),e.showPagination?(m(),i("div",w,[n(_,{"current-page":j.value,"page-size":A.value,"page-sizes":e.pageSizes,layout:e.paginationLayout,total:e.total,background:e.background,onSizeChange:handleSizeChange,onCurrentChange:handleCurrentChange,"onUpdate:currentPage":handleCurrentChange,"onUpdate:pageSize":handleSizeChange},null,8,["current-page","page-size","page-sizes","layout","total","background"])])):d("",!0)])),_:3})])}}}),[["__scopeId","data-v-85a7b344"]]);export{v as D};
