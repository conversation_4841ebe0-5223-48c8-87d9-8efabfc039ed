import{e,i as s,j as t,am as a,z as i,o}from"./vue-element-BXsXg3SF.js";import{a as l}from"./index-cttjCPxy.js";const r={class:"page-header"},c={class:"title-section"},d={class:"page-title"},p={class:"action-section"},n=l(e({__name:"PageHeader",props:{title:{type:String,required:!0}},setup:e=>(l,n)=>(o(),s("div",r,[t("div",c,[t("h1",d,i(e.title),1),a(l.$slots,"title-extra",{},void 0,!0)]),t("div",p,[a(l.$slots,"actions",{},void 0,!0)])]))}),[["__scopeId","data-v-07ce5512"]]);export{n as P};
