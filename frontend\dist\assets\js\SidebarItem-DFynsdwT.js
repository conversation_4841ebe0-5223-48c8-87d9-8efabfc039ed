import{a as e,u as t}from"./index-cttjCPxy.js";import{e as a,b as r,c as i,o as n,H as s,aM as l,aN as u,w as o,am as m,u as d,r as p,i as h,ad as c,k as f,aO as v,l as _,j as b,z as y,F as k,t as g,aP as x}from"./vue-element-BXsXg3SF.js";import"./utils-OxSuZc4o.js";function isExternal(e){return/^(https?:|mailto:|tel:)/.test(e)}const P=e(a({__name:"Link",props:{to:{type:String,required:!0}},setup(e){const t=e,a=r((()=>isExternal(t.to)?"a":"router-link")),d=r((()=>{if(isExternal(t.to))return{href:t.to,target:"_blank",rel:"noopener"};return{to:(e=>{if(!e||""===e)return"/";if(isExternal(e))return e;let t=e.replace(/\/+/g,"/");return t.startsWith("/")||(t="/"+t),t.length>1&&t.endsWith("/")&&(t=t.slice(0,-1)),t})(t.to),replace:!1}}));return(e,t)=>(n(),i(s(a.value),l(u(d.value)),{default:o((()=>[m(e.$slots,"default",{},void 0,!0)])),_:3},16))}}),[["__scopeId","data-v-d68f12be"]]),S={key:0},w={key:1},E=e(a({__name:"SidebarItem",props:{item:{type:Object,required:!0},basePath:{type:String,default:""}},setup(e){const a=d(),l=t(),u=e,m=r((()=>{const e=u.item.children||[],t=l.settings.developerMode;return e.filter((e=>{if(e.meta&&e.meta.hidden)return!1;if(e.meta&&e.meta.requireDeveloperMode&&!t)return!1;if(e.meta&&e.meta.requireAdmin){const e=localStorage.getItem("user_info");let t=!1;if(e)try{const a=JSON.parse(e);t=1===a.role_id||2===a.role_id}catch(a){}if(!t)return!1}return!0}))})),E=r((()=>{const e=m.value.length;return 1===e||0===e})),q=r((()=>1===m.value.length?m.value[0]:0===m.value.length?{...u.item,path:"",noShowingChildren:!0}:null)),j=r((()=>{if(!u.item.meta||!u.item.meta.title)return!1;if(u.item.meta.hidden)return!1;if(u.item.meta.requireDeveloperMode&&!l.settings.developerMode)return!1;if(u.item.meta.requireAdmin){const t=localStorage.getItem("user_info");let a=!1;if(t)try{const e=JSON.parse(t);a=1===e.role_id||2===e.role_id}catch(e){}if(!a)return!1}return!0})),resolvePath=e=>{if(!e)return u.basePath;if(isExternal(e))return e;if(isExternal(u.basePath))return u.basePath;const t=u.basePath&&"/"!==u.basePath;if(e.startsWith("/"))return e;return(t?u.basePath.endsWith("/")?u.basePath.slice(0,-1):u.basePath:"")+(e.startsWith("/")?e:"/"+e)};return(t,r)=>{const l=_,u=v,m=p("sidebar-item",!0),d=x;return j.value?(n(),h("div",S,[E.value&&!q.value.children?(n(),h("div",{key:0,onClick:r[0]||(r[0]=()=>(e=>{if(!e)return;if(isExternal(e))return void window.open(e,"_blank");if(e!==a.currentRoute.value.path)try{a.push(e).catch((e=>{}))}catch(t){window.location.href=e}})(resolvePath(q.value.path))),class:"menu-item-wrapper"},[q.value.meta?(n(),i(P,{key:0,to:resolvePath(q.value.path)},{default:o((()=>[f(u,{index:resolvePath(q.value.path)},{title:o((()=>[b("span",null,y(q.value.meta.title),1)])),default:o((()=>[q.value.meta&&q.value.meta.icon?(n(),i(l,{key:0},{default:o((()=>[(n(),i(s(q.value.meta.icon)))])),_:1})):c("",!0)])),_:1},8,["index"])])),_:1},8,["to"])):c("",!0)])):(n(),i(d,{key:1,index:resolvePath(e.item.path),"popper-append-to-body":""},{title:o((()=>[e.item.meta&&e.item.meta.icon?(n(),i(l,{key:0},{default:o((()=>[(n(),i(s(e.item.meta.icon)))])),_:1})):c("",!0),e.item.meta&&e.item.meta.title?(n(),h("span",w,y(e.item.meta.title),1)):c("",!0)])),default:o((()=>[e.item.children?(n(!0),h(k,{key:0},g(e.item.children,((e,t)=>(n(),h("div",{key:e.path||t},[e.meta&&e.meta.hidden?c("",!0):(n(),i(m,{key:0,item:e,"base-path":resolvePath(e.path)},null,8,["item","base-path"]))])))),128)):c("",!0)])),_:1},8,["index"]))])):c("",!0)}}}),[["__scopeId","data-v-fb5c5e52"]]);export{E as default};
