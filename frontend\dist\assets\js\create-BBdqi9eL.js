import{a as e}from"./index-cttjCPxy.js";/* empty css                *//* empty css                     *//* empty css                *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                 */import{e as a,a as l,Q as s,g as t,i as r,k as o,w as i,m as d,a5 as u,J as m,a0 as p,D as n,S as c,V as f,W as _,a6 as g,aG as v,aL as b,u as h,o as y}from"./vue-element-BXsXg3SF.js";import{P as j}from"./OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js";/* empty css                      *//* empty css                  *//* empty css                        *//* empty css                 */import{a as V,c as k}from"./role-CoSkS4Zr.js";import"./utils-OxSuZc4o.js";import"./http-CMGX6FrQ.js";const x={class:"role-create-container"},w=e(a({__name:"create",setup(e){const a=h(),w=l(),C=l(),q=s({name:"",code:"",status:"active",description:"",permissions:[]}),U=s({name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],code:[{required:!0,message:"请输入角色编码",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}),K=l([]),z=l(!1),handlePermissionCheck=(e,a)=>{q.permissions=a.checkedKeys},submitForm=async()=>{w.value&&await w.value.validate((async(e,l)=>{if(e){z.value=!0;try{await k(q),m.success("创建成功"),a.push("/role/list")}catch(s){m.error("创建失败")}finally{z.value=!1}}}))},resetForm=()=>{w.value&&w.value.resetFields(),C.value&&C.value.setCheckedKeys([])},goBack=()=>{a.push("/role/list")};return t((()=>{(async()=>{try{const e=await V();K.value=e.map((e=>({id:e.id,name:e.name,code:e.code,description:e.description})))}catch(e){m.error("获取权限列表失败")}})()})),(e,a)=>{const l=p,s=_,t=f,m=v,h=g,V=b,k=c,I=u;return y(),r("div",x,[o(d(j),{title:"新增角色"},{actions:i((()=>[o(l,{onClick:goBack},{default:i((()=>a[4]||(a[4]=[n("返回")]))),_:1})])),_:1}),o(I,{class:"form-card"},{default:i((()=>[o(k,{ref_key:"formRef",ref:w,model:q,rules:U,"label-width":"100px","status-icon":""},{default:i((()=>[o(t,{label:"角色名称",prop:"name"},{default:i((()=>[o(s,{modelValue:q.name,"onUpdate:modelValue":a[0]||(a[0]=e=>q.name=e),placeholder:"请输入角色名称",clearable:""},null,8,["modelValue"])])),_:1}),o(t,{label:"角色编码",prop:"code"},{default:i((()=>[o(s,{modelValue:q.code,"onUpdate:modelValue":a[1]||(a[1]=e=>q.code=e),placeholder:"请输入角色编码",clearable:""},null,8,["modelValue"])])),_:1}),o(t,{label:"状态",prop:"status"},{default:i((()=>[o(h,{modelValue:q.status,"onUpdate:modelValue":a[2]||(a[2]=e=>q.status=e)},{default:i((()=>[o(m,{label:"active"},{default:i((()=>a[5]||(a[5]=[n("启用")]))),_:1}),o(m,{label:"inactive"},{default:i((()=>a[6]||(a[6]=[n("禁用")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),o(t,{label:"描述",prop:"description"},{default:i((()=>[o(s,{modelValue:q.description,"onUpdate:modelValue":a[3]||(a[3]=e=>q.description=e),type:"textarea",rows:"4",placeholder:"请输入角色描述"},null,8,["modelValue"])])),_:1}),o(t,{label:"权限",prop:"permissions"},{default:i((()=>[o(V,{ref_key:"permissionTreeRef",ref:C,data:K.value,"show-checkbox":"","node-key":"id",props:{label:"name",children:"children"},onCheck:handlePermissionCheck},null,8,["data"])])),_:1}),o(t,null,{default:i((()=>[o(l,{type:"primary",onClick:submitForm,loading:z.value},{default:i((()=>a[7]||(a[7]=[n(" 保存 ")]))),_:1},8,["loading"]),o(l,{onClick:resetForm},{default:i((()=>a[8]||(a[8]=[n("重置")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})])}}}),[["__scopeId","data-v-09c0f051"]]);export{w as default};
