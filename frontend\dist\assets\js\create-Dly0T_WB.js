import{a as e}from"./index-cttjCPxy.js";/* empty css                *//* empty css                     *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                 */import{e as a,a as l,Q as r,i as s,k as o,w as t,m as u,a5 as d,a0 as m,D as i,l as p,av as n,S as c,V as g,W as f,a8 as _,a9 as b,a6 as v,aG as h,au as w,c as V,af as j,J as y,u as k,o as q}from"./vue-element-BXsXg3SF.js";import{P}from"./OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js";/* empty css                      *//* empty css                        *//* empty css                    *//* empty css                 */import{c as U}from"./user-C27kkjj6.js";import"./utils-OxSuZc4o.js";import"./http-CMGX6FrQ.js";const x={class:"user-create-container"},C=["src"],z=e(a({__name:"create",setup(e){const a=k(),z=l(),G=l(!1),B=r({username:"",password:"",confirmPassword:"",name:"",email:"",phone:"",role:"user",status:"active",avatar:"",permissions:[]}),I=r({username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能小于6个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入密码",trigger:"blur"},{validator:(e,a,l)=>{a!==B.password?l(new Error("两次输入密码不一致")):l()},trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}),handleAvatarSuccess=e=>{B.avatar=e.data.url},beforeAvatarUpload=e=>{const a="image/jpeg"===e.type||"image/png"===e.type,l=e.size/1024/1024<2;return a||y.error("上传头像图片只能是 JPG 或 PNG 格式!"),l||y.error("上传头像图片大小不能超过 2MB!"),a&&l},submitForm=async()=>{z.value&&await z.value.validate((async e=>{if(e){G.value=!0;try{const e={username:B.username,password:B.password,name:B.name,email:B.email,phone:B.phone,role:B.role,status:B.status,avatar:B.avatar,permissions:B.permissions};await U(e),y.success("用户创建成功"),a.push("/user/list")}catch(l){y.error("创建用户失败")}finally{G.value=!1}}else y.error("请完善表单信息")}))},resetForm=()=>{z.value&&z.value.resetFields()},goBack=()=>{a.push("/user/list")};return(e,a)=>{const l=p,r=m,y=f,k=g,U=b,J=_,D=h,E=v,F=w,H=c,M=d;return q(),s("div",x,[o(u(P),{title:"新增用户"},{actions:t((()=>[o(r,{onClick:goBack},{default:t((()=>[o(l,null,{default:t((()=>[o(u(n))])),_:1}),a[8]||(a[8]=i("返回列表 "))])),_:1})])),_:1}),o(M,{class:"form-card"},{default:t((()=>[o(H,{ref_key:"formRef",ref:z,model:B,rules:I,"label-width":"100px","status-icon":""},{default:t((()=>[o(k,{label:"用户名",prop:"username"},{default:t((()=>[o(y,{modelValue:B.username,"onUpdate:modelValue":a[0]||(a[0]=e=>B.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),o(k,{label:"密码",prop:"password"},{default:t((()=>[o(y,{modelValue:B.password,"onUpdate:modelValue":a[1]||(a[1]=e=>B.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])])),_:1}),o(k,{label:"确认密码",prop:"confirmPassword"},{default:t((()=>[o(y,{modelValue:B.confirmPassword,"onUpdate:modelValue":a[2]||(a[2]=e=>B.confirmPassword=e),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])])),_:1}),o(k,{label:"姓名",prop:"name"},{default:t((()=>[o(y,{modelValue:B.name,"onUpdate:modelValue":a[3]||(a[3]=e=>B.name=e),placeholder:"请输入姓名"},null,8,["modelValue"])])),_:1}),o(k,{label:"邮箱",prop:"email"},{default:t((()=>[o(y,{modelValue:B.email,"onUpdate:modelValue":a[4]||(a[4]=e=>B.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1}),o(k,{label:"手机号",prop:"phone"},{default:t((()=>[o(y,{modelValue:B.phone,"onUpdate:modelValue":a[5]||(a[5]=e=>B.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])])),_:1}),o(k,{label:"角色",prop:"role"},{default:t((()=>[o(J,{modelValue:B.role,"onUpdate:modelValue":a[6]||(a[6]=e=>B.role=e),placeholder:"请选择角色"},{default:t((()=>[o(U,{label:"管理员",value:"admin"}),o(U,{label:"普通用户",value:"user"}),o(U,{label:"访客",value:"guest"})])),_:1},8,["modelValue"])])),_:1}),o(k,{label:"状态",prop:"status"},{default:t((()=>[o(E,{modelValue:B.status,"onUpdate:modelValue":a[7]||(a[7]=e=>B.status=e)},{default:t((()=>[o(D,{label:"active"},{default:t((()=>a[9]||(a[9]=[i("启用")]))),_:1}),o(D,{label:"inactive"},{default:t((()=>a[10]||(a[10]=[i("禁用")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),o(k,{label:"头像",prop:"avatar"},{default:t((()=>[o(F,{class:"avatar-uploader",action:"/api/upload","show-file-list":!1,"on-success":handleAvatarSuccess,"before-upload":beforeAvatarUpload},{default:t((()=>[B.avatar?(q(),s("img",{key:0,src:B.avatar,class:"avatar"},null,8,C)):(q(),V(l,{key:1,class:"avatar-uploader-icon"},{default:t((()=>[o(u(j))])),_:1}))])),_:1})])),_:1}),o(k,null,{default:t((()=>[o(r,{type:"primary",onClick:submitForm,loading:G.value},{default:t((()=>a[11]||(a[11]=[i(" 保存 ")]))),_:1},8,["loading"]),o(r,{onClick:resetForm},{default:t((()=>a[12]||(a[12]=[i("重置")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})])}}}),[["__scopeId","data-v-b6acb337"]]);export{z as default};
