import{a as e}from"./index-cttjCPxy.js";/* empty css                *//* empty css                     *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css               *//* empty css                  *//* empty css                       *//* empty css                   */import{e as a,a as l,Q as t,g as r,i as o,j as n,k as u,w as s,a5 as i,S as d,ar as p,D as c,a3 as _,a4 as m,V as f,a2 as g,a8 as h,a9 as b,W as v,as as V,at as y,au as w,l as U,m as D,af as k,a0 as Y,J as j,u as q,o as x}from"./vue-element-BXsXg3SF.js";import"./utils-OxSuZc4o.js";const M={class:"page-container"},C=e(a({__name:"create",setup(e){const a=q(),C=l(),S=l(!1),E=l([]),I=[{text:"今天",value:new Date},{text:"昨天",value:()=>{const e=new Date;return e.setTime(e.getTime()-864e5),e}},{text:"一周前",value:()=>{const e=new Date;return e.setTime(e.getTime()-6048e5),e}}],T=t({transfer_time:"",transfer_type:"",recipient_account_name:"",recipient_account_id:"",channel_sales_name:"",person_in_charge:"",business_platform:"",business_remarks:"",currency:0,port_policy:0,customer_policy:0,port_recharge:0,receivable_amount:0,reserve_fund:0,self_advance:0,capital_advance:0,profit:0,arrival_time:"",arrival_amount:0,arrival_name:"",collector:"",outstanding_amount:0,capital_flow:"",audit_status:"未稽核",attachments:[],remark:""}),$=t({transfer_time:[{required:!0,message:"请选择转账时间",trigger:"blur"}],transfer_type:[{required:!0,message:"请选择转账类型",trigger:"change"}],recipient_account_name:[{required:!0,message:"请输入转入方账户名称",trigger:"blur"}],recipient_account_id:[{required:!0,message:"请输入转入方账户ID",trigger:"blur"}],channel_sales_name:[{required:!0,message:"请输入渠道/销售名称",trigger:"blur"}],person_in_charge:[{required:!0,message:"请输入负责人",trigger:"blur"}],business_platform:[{required:!0,message:"请选择业务平台",trigger:"change"}],currency:[{required:!0,message:"请输入户币",trigger:"blur"}],port_policy:[{required:!0,message:"请输入端口政策",trigger:"blur"}],customer_policy:[{required:!0,message:"请输入客户政策",trigger:"blur"}],port_recharge:[{required:!1,message:"端口充值款将自动计算",trigger:"blur"}],receivable_amount:[{required:!1,message:"应收款将自动计算",trigger:"blur"}],profit:[{required:!1,message:"利润将自动计算",trigger:"blur"}],audit_status:[{required:!0,message:"稽核状态默认为未稽核",trigger:"change"}]}),handleFileChange=e=>{E.value.push(e)},handleExceed=()=>{j.warning("最多只能上传5个文件")},submitForm=()=>{C.value&&C.value.validate((e=>{if(e){S.value=!0;try{const e=new FormData;["transfer_time","recipient_account_name","recipient_account_id","channel_sales_name","person_in_charge","business_platform","transfer_type","currency","port_policy","customer_policy"].forEach((a=>{e.append(a,T[a])}));["arrival_time","arrival_amount","arrival_name","collector","capital_flow","remark","business_remarks"].forEach((a=>{null!==T[a]&&void 0!==T[a]&&""!==T[a]&&e.append(a,T[a])})),E.value.forEach((a=>{a.raw&&e.append("files",a.raw)})),fetch("/api/sales",{method:"POST",body:e,headers:{Authorization:`Bearer ${localStorage.getItem("token")||""}`}}).then((e=>e.ok?e.json():e.json().then((e=>{throw new Error(e.detail||"添加失败")})))).then((()=>{j.success("添加成功"),a.push("/sales/list")})).catch((e=>{j.error(e.message||"添加销售数据失败")})).finally((()=>{S.value=!1}))}catch(l){j.error("添加销售数据失败"),S.value=!1}}else j.error("请完善表单信息")}))},resetForm=()=>{C.value&&(C.value.resetFields(),E.value=[])},goBack=()=>{a.push("/sales/list")},calculateAmounts=()=>{const e=T.port_policy/100,a=T.customer_policy/100;T.port_recharge=T.currency/(1+e),T.receivable_amount=T.currency/(1+a),"对公"===T.transfer_type?(T.self_advance=T.currency-T.port_recharge,T.capital_advance=0):(T.self_advance=T.currency-T.currency/(1+e-.025),T.capital_advance=T.currency-T.receivable_amount-T.self_advance),T.profit=T.receivable_amount-T.port_recharge,calculateOutstanding()},calculateOutstanding=()=>{T.outstanding_amount=T.receivable_amount-(T.arrival_amount||0),T.reserve_fund=(T.arrival_amount||0)-T.receivable_amount};return r((()=>{const e=new Date,a=e.getFullYear(),l=String(e.getMonth()+1).padStart(2,"0"),t=String(e.getDate()).padStart(2,"0");T.transfer_time=`${a}-${l}-${t}`,T.transfer_type="对公",T.port_policy=2,T.customer_policy=2,T.business_platform="巨量千川",T.audit_status="未稽核",calculateAmounts()})),(e,a)=>{const l=p,t=g,r=f,j=m,q=b,F=h,B=_,z=v,A=V,J=y,O=U,P=w,Q=Y,R=d,W=i;return x(),o("div",M,[a[35]||(a[35]=n("div",{class:"page-header"},[n("h2",{class:"page-title"},"添加销售数据"),n("p",{class:"page-description"},"创建新的销售数据记录")],-1)),u(W,{shadow:"hover"},{default:s((()=>[u(R,{ref_key:"formRef",ref:C,model:T,rules:$,"label-width":"140px","label-position":"right",class:"sales-form"},{default:s((()=>[u(l,{"content-position":"left"},{default:s((()=>a[25]||(a[25]=[c("基本信息")]))),_:1}),u(B,{gutter:20},{default:s((()=>[u(j,{span:12},{default:s((()=>[u(r,{label:"转账时间",prop:"transfer_time"},{default:s((()=>[u(t,{modelValue:T.transfer_time,"onUpdate:modelValue":a[0]||(a[0]=e=>T.transfer_time=e),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"},clearable:!1,editable:!1,shortcuts:I,teleported:!1},null,8,["modelValue"])])),_:1})])),_:1}),u(j,{span:12},{default:s((()=>[u(r,{label:"转账类型",prop:"transfer_type"},{default:s((()=>[u(F,{modelValue:T.transfer_type,"onUpdate:modelValue":a[1]||(a[1]=e=>T.transfer_type=e),placeholder:"请选择转账类型",style:{width:"100%"}},{default:s((()=>[u(q,{label:"对公",value:"对公"}),u(q,{label:"对私",value:"对私"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(l,{"content-position":"left"},{default:s((()=>a[26]||(a[26]=[c("账户信息")]))),_:1}),u(B,{gutter:20},{default:s((()=>[u(j,{span:12},{default:s((()=>[u(r,{label:"转入方账户名称",prop:"recipient_account_name"},{default:s((()=>[u(z,{modelValue:T.recipient_account_name,"onUpdate:modelValue":a[2]||(a[2]=e=>T.recipient_account_name=e),placeholder:"请输入账户名称"},null,8,["modelValue"])])),_:1})])),_:1}),u(j,{span:12},{default:s((()=>[u(r,{label:"转入方账户ID",prop:"recipient_account_id"},{default:s((()=>[u(z,{modelValue:T.recipient_account_id,"onUpdate:modelValue":a[3]||(a[3]=e=>T.recipient_account_id=e),placeholder:"请输入账户ID"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(l,{"content-position":"left"},{default:s((()=>a[27]||(a[27]=[c("业务信息")]))),_:1}),u(B,{gutter:20},{default:s((()=>[u(j,{span:12},{default:s((()=>[u(r,{label:"渠道/销售名称",prop:"channel_sales_name"},{default:s((()=>[u(z,{modelValue:T.channel_sales_name,"onUpdate:modelValue":a[4]||(a[4]=e=>T.channel_sales_name=e),placeholder:"请输入渠道或销售名称"},null,8,["modelValue"])])),_:1})])),_:1}),u(j,{span:12},{default:s((()=>[u(r,{label:"负责人",prop:"person_in_charge"},{default:s((()=>[u(z,{modelValue:T.person_in_charge,"onUpdate:modelValue":a[5]||(a[5]=e=>T.person_in_charge=e),placeholder:"请输入负责人姓名"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(B,{gutter:20},{default:s((()=>[u(j,{span:12},{default:s((()=>[u(r,{label:"业务平台",prop:"business_platform"},{default:s((()=>[u(F,{modelValue:T.business_platform,"onUpdate:modelValue":a[6]||(a[6]=e=>T.business_platform=e),placeholder:"请选择业务平台",style:{width:"100%"}},{default:s((()=>[u(q,{label:"巨量千川",value:"巨量千川"}),u(q,{label:"巨量本地推",value:"巨量本地推"}),u(q,{label:"巨量广告",value:"巨量广告"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),u(j,{span:12},{default:s((()=>[u(r,{label:"业务备注",prop:"business_remarks"},{default:s((()=>[u(z,{modelValue:T.business_remarks,"onUpdate:modelValue":a[7]||(a[7]=e=>T.business_remarks=e),placeholder:"请输入业务备注"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(l,{"content-position":"left"},{default:s((()=>a[28]||(a[28]=[c("政策与金额信息")]))),_:1}),u(B,{gutter:20},{default:s((()=>[u(j,{span:8},{default:s((()=>[u(r,{label:"户币",prop:"currency"},{default:s((()=>[u(A,{modelValue:T.currency,"onUpdate:modelValue":a[8]||(a[8]=e=>T.currency=e),precision:2,step:100,min:0,"controls-position":"right",style:{width:"100%"},onChange:calculateAmounts},null,8,["modelValue"])])),_:1})])),_:1}),u(j,{span:8},{default:s((()=>[u(r,{label:"端口政策(百分比)",prop:"port_policy"},{default:s((()=>[u(J,{content:"端口政策以百分比形式输入，如 2 表示 2%",placement:"top",effect:"light"},{default:s((()=>[u(A,{modelValue:T.port_policy,"onUpdate:modelValue":a[9]||(a[9]=e=>T.port_policy=e),precision:3,step:.1,min:0,max:100,"controls-position":"right",style:{width:"100%"},onChange:calculateAmounts},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(j,{span:8},{default:s((()=>[u(r,{label:"客户政策(百分比)",prop:"customer_policy"},{default:s((()=>[u(J,{content:"客户政策以百分比形式输入，如 2 表示 2%",placement:"top",effect:"light"},{default:s((()=>[u(A,{modelValue:T.customer_policy,"onUpdate:modelValue":a[10]||(a[10]=e=>T.customer_policy=e),precision:2,step:1,min:0,max:100,"controls-position":"right",style:{width:"100%"},onChange:calculateAmounts},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),u(B,{gutter:20},{default:s((()=>[u(j,{span:8},{default:s((()=>[u(r,{label:"端口充值款",prop:"port_recharge"},{default:s((()=>[u(J,{content:"自动计算：户币/(1+端口政策/100)",placement:"top",effect:"light"},{default:s((()=>[u(A,{modelValue:T.port_recharge,"onUpdate:modelValue":a[11]||(a[11]=e=>T.port_recharge=e),precision:2,step:100,min:0,"controls-position":"right",style:{width:"100%"},disabled:!0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(j,{span:8},{default:s((()=>[u(r,{label:"应收款",prop:"receivable_amount"},{default:s((()=>[u(J,{content:"自动计算：户币/(1+客户政策/100)",placement:"top",effect:"light"},{default:s((()=>[u(A,{modelValue:T.receivable_amount,"onUpdate:modelValue":a[12]||(a[12]=e=>T.receivable_amount=e),precision:2,step:100,min:0,"controls-position":"right",style:{width:"100%"},disabled:!0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(j,{span:8},{default:s((()=>[u(r,{label:"利润",prop:"profit"},{default:s((()=>[u(J,{content:"自动计算：应收款-端口充值款",placement:"top",effect:"light"},{default:s((()=>[u(A,{modelValue:T.profit,"onUpdate:modelValue":a[13]||(a[13]=e=>T.profit=e),precision:2,step:100,min:0,"controls-position":"right",style:{width:"100%"},disabled:!0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),u(l,{"content-position":"left"},{default:s((()=>a[29]||(a[29]=[c("资金信息")]))),_:1}),u(B,{gutter:20},{default:s((()=>[u(j,{span:8},{default:s((()=>[u(r,{label:"备款",prop:"reserve_fund"},{default:s((()=>[u(J,{content:"自动计算：来款-应收款",placement:"top",effect:"light"},{default:s((()=>[u(A,{modelValue:T.reserve_fund,"onUpdate:modelValue":a[14]||(a[14]=e=>T.reserve_fund=e),precision:2,step:100,"controls-position":"right",style:{width:"100%"},disabled:!0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(j,{span:8},{default:s((()=>[u(r,{label:"自行垫款",prop:"self_advance"},{default:s((()=>[u(J,{content:"对公"===T.transfer_type?"自动计算：户币-端口充值款":"自动计算：户币-户币/(1+端口政策/100-0.025)",placement:"top",effect:"light"},{default:s((()=>[u(A,{modelValue:T.self_advance,"onUpdate:modelValue":a[15]||(a[15]=e=>T.self_advance=e),precision:2,step:100,"controls-position":"right",style:{width:"100%"},disabled:!0},null,8,["modelValue"])])),_:1},8,["content"])])),_:1})])),_:1}),u(j,{span:8},{default:s((()=>[u(r,{label:"资方垫款",prop:"capital_advance"},{default:s((()=>[u(J,{content:"对公"===T.transfer_type?"对公转账时资方垫款为0":"自动计算：户币-应收款-自行垫款",placement:"top",effect:"light"},{default:s((()=>[u(A,{modelValue:T.capital_advance,"onUpdate:modelValue":a[16]||(a[16]=e=>T.capital_advance=e),precision:2,step:100,"controls-position":"right",style:{width:"100%"},disabled:!0},null,8,["modelValue"])])),_:1},8,["content"])])),_:1})])),_:1})])),_:1}),u(l,{"content-position":"left"},{default:s((()=>a[30]||(a[30]=[c("到账信息")]))),_:1}),u(B,{gutter:20},{default:s((()=>[u(j,{span:8},{default:s((()=>[u(r,{label:"到账时间",prop:"arrival_time"},{default:s((()=>[u(t,{modelValue:T.arrival_time,"onUpdate:modelValue":a[17]||(a[17]=e=>T.arrival_time=e),type:"date",placeholder:"选择到账时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"},clearable:!0,editable:!1,shortcuts:I,teleported:!1},null,8,["modelValue"])])),_:1})])),_:1}),u(j,{span:8},{default:s((()=>[u(r,{label:"到账金额",prop:"arrival_amount"},{default:s((()=>[u(A,{modelValue:T.arrival_amount,"onUpdate:modelValue":a[18]||(a[18]=e=>T.arrival_amount=e),precision:2,step:100,min:0,"controls-position":"right",style:{width:"100%"},onChange:calculateOutstanding},null,8,["modelValue"])])),_:1})])),_:1}),u(j,{span:8},{default:s((()=>[u(r,{label:"欠款金额",prop:"outstanding_amount"},{default:s((()=>[u(J,{content:"自动计算：应收款-到账金额",placement:"top",effect:"light"},{default:s((()=>[u(A,{modelValue:T.outstanding_amount,"onUpdate:modelValue":a[19]||(a[19]=e=>T.outstanding_amount=e),precision:2,step:100,min:0,"controls-position":"right",style:{width:"100%"},disabled:!0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),u(B,{gutter:20},{default:s((()=>[u(j,{span:12},{default:s((()=>[u(r,{label:"到账名称",prop:"arrival_name"},{default:s((()=>[u(z,{modelValue:T.arrival_name,"onUpdate:modelValue":a[20]||(a[20]=e=>T.arrival_name=e),placeholder:"请输入到账名称"},null,8,["modelValue"])])),_:1})])),_:1}),u(j,{span:12},{default:s((()=>[u(r,{label:"收款人",prop:"collector"},{default:s((()=>[u(z,{modelValue:T.collector,"onUpdate:modelValue":a[21]||(a[21]=e=>T.collector=e),placeholder:"请输入收款人"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(B,{gutter:20},{default:s((()=>[u(j,{span:12},{default:s((()=>[u(r,{label:"资金流向",prop:"capital_flow"},{default:s((()=>[u(z,{modelValue:T.capital_flow,"onUpdate:modelValue":a[22]||(a[22]=e=>T.capital_flow=e),placeholder:"请输入资金流向"},null,8,["modelValue"])])),_:1})])),_:1}),u(j,{span:12},{default:s((()=>[u(r,{label:"稽核状态",prop:"audit_status"},{default:s((()=>[u(J,{content:"新建数据默认为未稽核状态，需要在稽核页面进行稽核操作",placement:"top",effect:"light"},{default:s((()=>[u(z,{modelValue:T.audit_status,"onUpdate:modelValue":a[23]||(a[23]=e=>T.audit_status=e),placeholder:"未稽核",disabled:!0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),u(r,{label:"附件",prop:"attachments"},{tip:s((()=>a[31]||(a[31]=[n("div",{class:"el-upload__tip"},"支持jpg/png格式，单个文件不超过2MB",-1)]))),default:s((()=>[u(P,{action:"#","list-type":"picture-card","auto-upload":!1,limit:5,"on-exceed":handleExceed,"on-change":handleFileChange,"file-list":E.value},{default:s((()=>[u(O,null,{default:s((()=>[u(D(k))])),_:1})])),_:1},8,["file-list"])])),_:1}),u(r,{label:"备注",prop:"remark"},{default:s((()=>[u(z,{modelValue:T.remark,"onUpdate:modelValue":a[24]||(a[24]=e=>T.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1}),u(r,null,{default:s((()=>[u(Q,{type:"primary",onClick:submitForm,loading:S.value},{default:s((()=>a[32]||(a[32]=[c("保存")]))),_:1},8,["loading"]),u(Q,{onClick:resetForm},{default:s((()=>a[33]||(a[33]=[c("重置")]))),_:1}),u(Q,{onClick:goBack},{default:s((()=>a[34]||(a[34]=[c("返回")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})])}}}),[["__scopeId","data-v-c0a9226d"]]);export{C as default};
