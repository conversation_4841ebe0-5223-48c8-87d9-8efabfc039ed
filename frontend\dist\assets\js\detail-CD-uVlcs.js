import{a}from"./index-cttjCPxy.js";/* empty css                *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                   *//* empty css                             */import{e,a as t,g as l,i as s,j as r,k as n,w as i,a0 as u,a5 as o,f as d,D as c,l as p,m,av as v,c as f,aw as y,ax as _,z as h,ac as b,ar as g,aa as w,ab as j,ay as k,az as A,aA as P,aB as x,aC as C,aD as N,F as z,t as F,aE as S,aF as T,ao as q,u as M,o as B}from"./vue-element-BXsXg3SF.js";import"./utils-OxSuZc4o.js";const D={class:"sales-detail-container"},E={class:"page-header"},I={class:"remark-content"},V=a(e({__name:"detail",setup(a){const e=M(),V=d(),$=t({id:0,date:"",salesperson:"",customerName:"",contactPerson:"",contactPhone:"",customerType:"",productName:"",productCategory:"",quantity:0,unitPrice:0,totalAmount:0,paymentMethod:"",paymentStatus:"",status:"",remark:"",createdAt:"",updatedAt:""}),G=t([]),goBack=()=>{e.push("/sales/list")},formatPrice=a=>`¥ ${a.toFixed(2)}`,getStatusText=a=>({draft:"草稿",pending:"待审核",approved:"已审核",rejected:"已驳回"}[a]||"未知状态"),getCustomerTypeText=a=>({new:"新客户",returning:"老客户",vip:"VIP客户"}[a]||"未知类型"),getPaymentStatusText=a=>({unpaid:"未支付",partial:"部分支付",paid:"已支付"}[a]||"未知状态");return l((()=>{"1"===V.params.id&&($.value={id:1,date:"2023-04-01",salesperson:"zhangsan",customerName:"北京科技有限公司",contactPerson:"张经理",contactPhone:"***********",customerType:"returning",productName:"高级会员服务",productCategory:"membership",quantity:1,unitPrice:9800,totalAmount:9800,paymentMethod:"bank",paymentStatus:"paid",status:"approved",remark:"年度服务续费",createdAt:"2023-04-01 09:15:30",updatedAt:"2023-04-02 14:22:45"},G.value=[{time:"2023-04-01 09:15:30",content:"销售记录创建 - 张三",type:"info"},{time:"2023-04-01 10:30:15",content:"提交审核 - 张三",type:"primary"},{time:"2023-04-02 14:22:45",content:"财务审核通过 - 王财务",type:"success"}])})),(a,e)=>{const t=p,l=u,d=_,M=b,V=y,H=g,J=j,K=w,L=A,O=k,Q=S,R=T,U=q,W=o;return B(),s("div",D,[r("div",E,[e[1]||(e[1]=r("h1",{class:"page-title"},"销售详情",-1)),n(l,{onClick:goBack},{default:i((()=>[n(t,null,{default:i((()=>[n(m(v))])),_:1}),e[0]||(e[0]=c("返回列表 "))])),_:1})]),n(W,{class:"detail-card"},{default:i((()=>{return[n(V,{title:"基本信息",column:2,border:""},{default:i((()=>[n(d,{label:"销售编号"},{default:i((()=>[c(h($.value.id),1)])),_:1}),n(d,{label:"销售日期"},{default:i((()=>[c(h($.value.date),1)])),_:1}),n(d,{label:"销售员"},{default:i((()=>{return[c(h((a=$.value.salesperson,{zhangsan:"张三",lisi:"李四",wangwu:"王五"}[a]||a)),1)];var a})),_:1}),n(d,{label:"销售状态"},{default:i((()=>{return[n(M,{type:(a=$.value.status,{draft:"info",pending:"warning",approved:"success",rejected:"danger"}[a]||"info")},{default:i((()=>[c(h(getStatusText($.value.status)),1)])),_:1},8,["type"])];var a})),_:1})])),_:1}),n(H),n(V,{title:"客户信息",column:2,border:""},{default:i((()=>[n(d,{label:"客户名称"},{default:i((()=>[c(h($.value.customerName),1)])),_:1}),n(d,{label:"客户类型"},{default:i((()=>{return[n(M,{size:"small",type:(a=$.value.customerType,{new:"",returning:"success",vip:"warning"}[a]||"")},{default:i((()=>[c(h(getCustomerTypeText($.value.customerType)),1)])),_:1},8,["type"])];var a})),_:1}),n(d,{label:"联系人"},{default:i((()=>[c(h($.value.contactPerson),1)])),_:1}),n(d,{label:"联系电话"},{default:i((()=>[c(h($.value.contactPhone),1)])),_:1})])),_:1}),n(H),e[2]||(e[2]=r("h3",{class:"section-title"},"产品信息",-1)),n(K,{data:[$.value],style:{width:"100%"},border:""},{default:i((()=>[n(J,{prop:"productName",label:"产品名称","min-width":"180"}),n(J,{prop:"productCategory",label:"产品类别","min-width":"120"},{default:i((({row:a})=>{return[c(h((e=a.productCategory,{membership:"会员服务",technical:"技术支持",advertising:"广告服务",data:"数据服务"}[e]||"其他")),1)];var e})),_:1}),n(J,{prop:"quantity",label:"数量","min-width":"80"}),n(J,{prop:"unitPrice",label:"单价","min-width":"120"},{default:i((({row:a})=>[c(h(formatPrice(a.unitPrice)),1)])),_:1}),n(J,{prop:"totalAmount",label:"总金额","min-width":"120"},{default:i((({row:a})=>[c(h(formatPrice(a.totalAmount)),1)])),_:1})])),_:1},8,["data"]),n(H),n(V,{title:"支付信息",column:2,border:""},{default:i((()=>[n(d,{label:"付款方式"},{default:i((()=>{return[c(h((a=$.value.paymentMethod,{cash:"现金",bank:"银行转账",wechat:"微信支付",alipay:"支付宝",credit:"信用卡"}[a]||"其他")),1)];var a})),_:1}),n(d,{label:"付款状态"},{default:i((()=>{return[n(M,{type:(a=$.value.paymentStatus,{unpaid:"danger",partial:"warning",paid:"success"}[a]||"info")},{default:i((()=>[c(h(getPaymentStatusText($.value.paymentStatus)),1)])),_:1},8,["type"])];var a})),_:1}),n(d,{label:"创建时间"},{default:i((()=>[c(h($.value.createdAt),1)])),_:1}),n(d,{label:"更新时间"},{default:i((()=>[c(h($.value.updatedAt),1)])),_:1})])),_:1}),n(H),e[3]||(e[3]=r("h3",{class:"section-title"},"备注信息",-1)),r("div",I,h($.value.remark||"暂无备注"),1),n(H),e[4]||(e[4]=r("h3",{class:"section-title"},"审核信息",-1)),n(O,{active:(a=$.value.status,{draft:0,pending:1,approved:3,rejected:1}[a]||0),"finish-status":"success",simple:"",style:{"margin-top":"20px"}},{default:i((()=>[n(L,{title:"创建",icon:m(P)},null,8,["icon"]),n(L,{title:"提交审核",icon:m(x)},null,8,["icon"]),n(L,{title:"财务审核",icon:m(C)},null,8,["icon"]),n(L,{title:"完成",icon:m(N)},null,8,["icon"])])),_:1},8,["active"]),G.value.length>0?(B(),f(R,{key:0,class:"timeline"},{default:i((()=>[(B(!0),s(z,null,F(G.value,((a,e)=>(B(),f(Q,{key:e,timestamp:a.time,type:a.type,hollow:a.hollow,color:a.color},{default:i((()=>[c(h(a.content),1)])),_:2},1032,["timestamp","type","hollow","color"])))),128))])),_:1})):(B(),f(U,{key:1,description:"暂无审核记录"}))];var a})),_:1})])}}}),[["__scopeId","data-v-3ba78089"]]);export{V as default};
