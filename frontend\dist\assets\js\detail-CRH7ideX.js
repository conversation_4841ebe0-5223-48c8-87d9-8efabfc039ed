import{a}from"./index-cttjCPxy.js";/* empty css                      *//* empty css                *//* empty css                 *//* empty css                   *//* empty css                             */import{e,a as s,f as t,Q as i,g as l,i as r,k as o,ai as d,w as n,m as p,aj as u,c as m,J as c,a5 as _,a0 as f,D as j,j as v,ax as y,z as b,ac as g,aw as h,ar as k,F as x,t as w,ao as z,u as C,o as D}from"./vue-element-BXsXg3SF.js";import{P as F}from"./OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js";/* empty css                     *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import{b as I}from"./role-CoSkS4Zr.js";import"./utils-OxSuZc4o.js";import"./http-CMGX6FrQ.js";const J={class:"role-detail-container"},O={class:"permission-section"},q={key:0},B=a(e({__name:"detail",setup(a){const e=C(),B=t(),formatDate=a=>{if(!a)return"";return new Date(a).toLocaleString()},E=s(Number(B.params.id)||0),L=i({id:0,name:"",code:"",status:"",description:"",created_at:"",updated_at:"",permission_details:[]}),N=s(!1),goBack=()=>{e.push("/role/list")},handleEdit=()=>{e.push(`/role/edit/${E.value}`)};return l((()=>{(async()=>{if(E.value){N.value=!0;try{const a=await I(E.value);a&&Object.assign(L,a)}catch(a){c.error("获取角色详情失败")}finally{N.value=!1}}else c.error("角色ID不能为空")})()})),(a,e)=>{const s=f,t=y,i=g,l=h,c=k,C=z,I=_,B=u;return D(),r("div",J,[o(p(F),{title:"角色详情"},{actions:n((()=>[o(s,{onClick:goBack},{default:n((()=>e[0]||(e[0]=[j("返回")]))),_:1}),o(s,{type:"primary",onClick:handleEdit},{default:n((()=>e[1]||(e[1]=[j("编辑")]))),_:1})])),_:1}),d((D(),m(I,{class:"detail-card"},{default:n((()=>[o(l,{column:2,border:""},{default:n((()=>[o(t,{label:"角色名称"},{default:n((()=>[j(b(L.name),1)])),_:1}),o(t,{label:"角色编码"},{default:n((()=>[j(b(L.code),1)])),_:1}),o(t,{label:"状态"},{default:n((()=>[o(i,{type:"active"===L.status?"success":"danger"},{default:n((()=>[j(b("active"===L.status?"启用":"禁用"),1)])),_:1},8,["type"])])),_:1}),o(t,{label:"创建时间"},{default:n((()=>[j(b(formatDate(L.created_at)),1)])),_:1}),o(t,{label:"更新时间"},{default:n((()=>[j(b(formatDate(L.updated_at)),1)])),_:1}),o(t,{label:"描述",span:2},{default:n((()=>[j(b(L.description||"暂无描述"),1)])),_:1})])),_:1}),v("div",O,[e[2]||(e[2]=v("h3",null,"权限列表",-1)),o(c),L.permission_details&&L.permission_details.length?(D(),r("div",q,[(D(!0),r(x,null,w(L.permission_details,(a=>(D(),m(i,{key:a.id,class:"permission-tag"},{default:n((()=>[j(b(a.name),1)])),_:2},1024)))),128))])):(D(),m(C,{key:1,description:"暂无权限"}))])])),_:1})),[[B,N.value]])])}}}),[["__scopeId","data-v-9da4e688"]]);export{B as default};
