import{a}from"./index-cttjCPxy.js";/* empty css                      *//* empty css                *//* empty css                             */import{e,a as s,g as t,i as l,k as i,ai as r,w as u,m as n,aj as o,c as d,f as m,J as p,u as c,a5 as v,a0 as _,D as f,l as y,av as b,aH as j,j as g,ad as h,y as k,ax as x,z,ac as D,aw as w,F as C,t as I,o as F}from"./vue-element-BXsXg3SF.js";import{P as H}from"./OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js";/* empty css                     *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                 */import{a as N}from"./user-C27kkjj6.js";import"./utils-OxSuZc4o.js";import"./http-CMGX6FrQ.js";const q={class:"user-detail-container"},B={class:"user-info"},J={class:"avatar-container"},L={key:0,class:"permissions-section"},O=a(e({__name:"detail",setup(a){const e=c(),O=m(),P=s(!1),S=s({id:0,username:"",name:"",email:"",phone:"",role:"",status:"",avatar:"",permissions:[],created_at:"",updated_at:""}),formatDate=a=>{if(!a)return"";return new Date(a).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},getRoleName=a=>({admin:"管理员",user:"普通用户",guest:"访客"}[a]||a),handleEdit=()=>{e.push(`/user/edit/${S.value.id}`)},goBack=()=>{e.push("/user/list")};return t((()=>{(async()=>{const a=Number(O.params.id);if(!a)return p.error("用户ID无效"),void goBack();P.value=!0;try{const e=await N(a);S.value=e}catch(e){p.error("获取用户详情失败")}finally{P.value=!1}})()})),(a,e)=>{const s=y,t=_,m=k,p=x,c=D,N=w,O=v,$=o;return F(),l("div",q,[i(n(H),{title:"用户详情"},{actions:u((()=>[i(t,{onClick:goBack},{default:u((()=>[i(s,null,{default:u((()=>[i(n(b))])),_:1}),e[0]||(e[0]=f("返回列表 "))])),_:1}),i(t,{type:"primary",onClick:handleEdit},{default:u((()=>[i(s,null,{default:u((()=>[i(n(j))])),_:1}),e[1]||(e[1]=f("编辑 "))])),_:1})])),_:1}),r((F(),d(O,{class:"detail-card"},{default:u((()=>[g("div",B,[g("div",J,[i(m,{size:100,src:S.value.avatar||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"},null,8,["src"])]),i(N,{title:"基本信息",column:2,border:""},{default:u((()=>[i(p,{label:"用户ID"},{default:u((()=>[f(z(S.value.id),1)])),_:1}),i(p,{label:"用户名"},{default:u((()=>[f(z(S.value.username),1)])),_:1}),i(p,{label:"姓名"},{default:u((()=>[f(z(S.value.name),1)])),_:1}),i(p,{label:"角色"},{default:u((()=>{return[i(c,{type:(a=S.value.role,{admin:"danger",user:"primary",guest:"info"}[a]||"info")},{default:u((()=>[f(z(getRoleName(S.value.role)),1)])),_:1},8,["type"])];var a})),_:1}),i(p,{label:"邮箱"},{default:u((()=>[f(z(S.value.email),1)])),_:1}),i(p,{label:"手机号"},{default:u((()=>[f(z(S.value.phone||"未设置"),1)])),_:1}),i(p,{label:"状态"},{default:u((()=>[i(c,{type:"active"===S.value.status?"success":"danger"},{default:u((()=>[f(z("active"===S.value.status?"启用":"禁用"),1)])),_:1},8,["type"])])),_:1}),i(p,{label:"创建时间"},{default:u((()=>[f(z(formatDate(S.value.created_at)),1)])),_:1}),i(p,{label:"更新时间"},{default:u((()=>[f(z(formatDate(S.value.updated_at)),1)])),_:1})])),_:1})]),S.value.permissions&&S.value.permissions.length?(F(),l("div",L,[e[2]||(e[2]=g("h3",null,"权限列表",-1)),(F(!0),l(C,null,I(S.value.permissions,(a=>(F(),d(c,{key:a,class:"permission-tag"},{default:u((()=>[f(z(a),1)])),_:2},1024)))),128))])):h("",!0)])),_:1})),[[$,P.value]])])}}}),[["__scopeId","data-v-4a057687"]]);export{O as default};
