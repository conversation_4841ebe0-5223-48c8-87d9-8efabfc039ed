import{a as e}from"./index-cttjCPxy.js";/* empty css                *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                   *//* empty css               *//* empty css                  *//* empty css                       */import{e as a,a as l,b as t,f as u,Q as r,g as o,i as d,j as s,k as n,z as m,w as p,a0 as i,a5 as c,D as g,l as b,m as f,av as _,S as h,a3 as y,a4 as v,V,a2 as P,a8 as q,F as w,t as k,a9 as U,W as j,ar as N,as as C,J as M,u as Y,o as S}from"./vue-element-BXsXg3SF.js";import"./utils-OxSuZc4o.js";const T={class:"sales-edit-container"},x={class:"page-header"},A={class:"page-title"},D=e(a({__name:"edit",setup(e){const a=Y(),D=u(),z=l(),F=l(!1),I=t((()=>void 0!==D.params.id)),E=[{value:"zhangsan",label:"张三"},{value:"lisi",label:"李四"},{value:"wangwu",label:"王五"}],J=r({id:void 0,date:"",salesperson:"",customerName:"",contactPerson:"",contactPhone:"",customerType:"",productName:"",productCategory:"",quantity:1,unitPrice:0,totalAmount:0,paymentMethod:"",paymentStatus:"",remark:""}),O=r({date:[{required:!0,message:"请选择销售日期",trigger:"blur"}],salesperson:[{required:!0,message:"请选择销售员",trigger:"change"}],customerName:[{required:!0,message:"请输入客户名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],contactPerson:[{required:!0,message:"请输入联系人",trigger:"blur"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],customerType:[{required:!0,message:"请选择客户类型",trigger:"change"}],productName:[{required:!0,message:"请输入产品名称",trigger:"blur"}],productCategory:[{required:!0,message:"请选择产品类别",trigger:"change"}],quantity:[{required:!0,message:"请输入数量",trigger:"blur"},{type:"number",min:1,message:"数量必须大于0",trigger:"blur"}],unitPrice:[{required:!0,message:"请输入单价",trigger:"blur"},{type:"number",min:0,message:"单价必须大于等于0",trigger:"blur"}],paymentMethod:[{required:!0,message:"请选择付款方式",trigger:"change"}],paymentStatus:[{required:!0,message:"请选择付款状态",trigger:"change"}]}),calculateTotal=()=>{J.totalAmount=J.quantity*J.unitPrice},submitForm=async()=>{z.value&&await z.value.validate(((e,a)=>{e&&(F.value=!0,setTimeout((()=>{M.success("保存成功"),F.value=!1,goBack()}),1e3))}))},resetForm=()=>{z.value&&z.value.resetFields()},goBack=()=>{a.push("/sales/list")};return o((()=>{I.value&&I.value&&"1"===D.params.id&&Object.assign(J,{id:1,date:"2023-04-01",salesperson:"zhangsan",customerName:"北京科技有限公司",contactPerson:"张经理",contactPhone:"***********",customerType:"returning",productName:"高级会员服务",productCategory:"membership",quantity:1,unitPrice:9800,totalAmount:9800,paymentMethod:"bank",paymentStatus:"paid",remark:"年度服务续费"})})),(e,a)=>{const l=b,t=i,u=P,r=V,o=v,M=U,Y=q,D=y,Q=j,R=N,W=C,$=h,B=c;return S(),d("div",T,[s("div",x,[s("h1",A,m(I.value?"编辑销售数据":"新增销售数据"),1),n(t,{onClick:goBack},{default:p((()=>[n(l,null,{default:p((()=>[n(f(_))])),_:1}),a[14]||(a[14]=g("返回列表 "))])),_:1})]),n(B,{class:"form-card"},{default:p((()=>[n($,{ref_key:"formRef",ref:z,model:J,rules:O,"label-position":"right","label-width":"120px"},{default:p((()=>[n(D,{gutter:20},{default:p((()=>[n(o,{span:12},{default:p((()=>[n(r,{label:"销售日期",prop:"date"},{default:p((()=>[n(u,{modelValue:J.date,"onUpdate:modelValue":a[0]||(a[0]=e=>J.date=e),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),n(o,{span:12},{default:p((()=>[n(r,{label:"销售员",prop:"salesperson"},{default:p((()=>[n(Y,{modelValue:J.salesperson,"onUpdate:modelValue":a[1]||(a[1]=e=>J.salesperson=e),placeholder:"选择销售员",style:{width:"100%"}},{default:p((()=>[(S(),d(w,null,k(E,(e=>n(M,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(D,{gutter:20},{default:p((()=>[n(o,{span:12},{default:p((()=>[n(r,{label:"客户名称",prop:"customerName"},{default:p((()=>[n(Q,{modelValue:J.customerName,"onUpdate:modelValue":a[2]||(a[2]=e=>J.customerName=e),placeholder:"请输入客户名称"},null,8,["modelValue"])])),_:1})])),_:1}),n(o,{span:12},{default:p((()=>[n(r,{label:"客户联系人",prop:"contactPerson"},{default:p((()=>[n(Q,{modelValue:J.contactPerson,"onUpdate:modelValue":a[3]||(a[3]=e=>J.contactPerson=e),placeholder:"请输入联系人姓名"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(D,{gutter:20},{default:p((()=>[n(o,{span:12},{default:p((()=>[n(r,{label:"联系电话",prop:"contactPhone"},{default:p((()=>[n(Q,{modelValue:J.contactPhone,"onUpdate:modelValue":a[4]||(a[4]=e=>J.contactPhone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1})])),_:1}),n(o,{span:12},{default:p((()=>[n(r,{label:"客户类型",prop:"customerType"},{default:p((()=>[n(Y,{modelValue:J.customerType,"onUpdate:modelValue":a[5]||(a[5]=e=>J.customerType=e),placeholder:"选择客户类型",style:{width:"100%"}},{default:p((()=>[n(M,{label:"新客户",value:"new"}),n(M,{label:"老客户",value:"returning"}),n(M,{label:"VIP客户",value:"vip"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(R,{"content-position":"center"},{default:p((()=>a[15]||(a[15]=[g("产品信息")]))),_:1}),n(D,{gutter:20},{default:p((()=>[n(o,{span:12},{default:p((()=>[n(r,{label:"产品名称",prop:"productName"},{default:p((()=>[n(Q,{modelValue:J.productName,"onUpdate:modelValue":a[6]||(a[6]=e=>J.productName=e),placeholder:"请输入产品名称"},null,8,["modelValue"])])),_:1})])),_:1}),n(o,{span:12},{default:p((()=>[n(r,{label:"产品类别",prop:"productCategory"},{default:p((()=>[n(Y,{modelValue:J.productCategory,"onUpdate:modelValue":a[7]||(a[7]=e=>J.productCategory=e),placeholder:"选择产品类别",style:{width:"100%"}},{default:p((()=>[n(M,{label:"会员服务",value:"membership"}),n(M,{label:"技术支持",value:"technical"}),n(M,{label:"广告服务",value:"advertising"}),n(M,{label:"数据服务",value:"data"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(D,{gutter:20},{default:p((()=>[n(o,{span:8},{default:p((()=>[n(r,{label:"数量",prop:"quantity"},{default:p((()=>[n(W,{modelValue:J.quantity,"onUpdate:modelValue":a[8]||(a[8]=e=>J.quantity=e),min:1,precision:0,onChange:calculateTotal},null,8,["modelValue"])])),_:1})])),_:1}),n(o,{span:8},{default:p((()=>[n(r,{label:"单价(元)",prop:"unitPrice"},{default:p((()=>[n(W,{modelValue:J.unitPrice,"onUpdate:modelValue":a[9]||(a[9]=e=>J.unitPrice=e),min:0,precision:2,onChange:calculateTotal},null,8,["modelValue"])])),_:1})])),_:1}),n(o,{span:8},{default:p((()=>[n(r,{label:"总金额(元)",prop:"totalAmount"},{default:p((()=>[n(W,{modelValue:J.totalAmount,"onUpdate:modelValue":a[10]||(a[10]=e=>J.totalAmount=e),min:0,precision:2,disabled:!0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(D,{gutter:20},{default:p((()=>[n(o,{span:12},{default:p((()=>[n(r,{label:"付款方式",prop:"paymentMethod"},{default:p((()=>[n(Y,{modelValue:J.paymentMethod,"onUpdate:modelValue":a[11]||(a[11]=e=>J.paymentMethod=e),placeholder:"选择付款方式",style:{width:"100%"}},{default:p((()=>[n(M,{label:"现金",value:"cash"}),n(M,{label:"银行转账",value:"bank"}),n(M,{label:"微信支付",value:"wechat"}),n(M,{label:"支付宝",value:"alipay"}),n(M,{label:"信用卡",value:"credit"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),n(o,{span:12},{default:p((()=>[n(r,{label:"付款状态",prop:"paymentStatus"},{default:p((()=>[n(Y,{modelValue:J.paymentStatus,"onUpdate:modelValue":a[12]||(a[12]=e=>J.paymentStatus=e),placeholder:"选择付款状态",style:{width:"100%"}},{default:p((()=>[n(M,{label:"未支付",value:"unpaid"}),n(M,{label:"部分支付",value:"partial"}),n(M,{label:"已支付",value:"paid"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(r,{label:"备注",prop:"remark"},{default:p((()=>[n(Q,{modelValue:J.remark,"onUpdate:modelValue":a[13]||(a[13]=e=>J.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1}),n(r,null,{default:p((()=>[n(t,{type:"primary",onClick:submitForm,loading:F.value},{default:p((()=>a[16]||(a[16]=[g("保存")]))),_:1},8,["loading"]),n(t,{onClick:resetForm},{default:p((()=>a[17]||(a[17]=[g("重置")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})])}}}),[["__scopeId","data-v-c4b60556"]]);export{D as default};
