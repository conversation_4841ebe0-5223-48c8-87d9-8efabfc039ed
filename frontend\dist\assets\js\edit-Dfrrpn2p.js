import{a as e}from"./index-cttjCPxy.js";/* empty css                      *//* empty css                *//* empty css                     *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                 */import{e as a,a as l,Q as s,g as r,i as o,k as t,ai as u,w as d,m as i,aj as m,c as p,aI as n,f as c,J as f,u as g,a5 as v,a0 as _,D as w,l as h,av as b,V as y,W as V,a8 as j,a9 as k,a6 as P,aG as U,au as C,af as q,aJ as x,S as I,j as z,o as G}from"./vue-element-BXsXg3SF.js";import{P as J}from"./OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js";/* empty css                        *//* empty css                    *//* empty css                 */import{a as B,u as D,r as N}from"./user-C27kkjj6.js";import"./utils-OxSuZc4o.js";import"./http-CMGX6FrQ.js";const R={class:"user-edit-container"},E=["src"],F={class:"dialog-footer"},H=e(a({__name:"edit",setup(e){const a=g(),H=c(),M=l(),O=l(),Q=l(!1),S=l(!1),W=l(!1),$=l(!1),A=s({id:0,username:"",name:"",email:"",phone:"",role:"",status:"",avatar:"",permissions:[]}),K=s({password:"",confirmPassword:""}),L=s({name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}),T=s({password:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能小于6个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(e,a,l)=>{a!==K.password?l(new Error("两次输入密码不一致")):l()},trigger:"blur"}]}),getUserData=async()=>{const e=Number(H.params.id);if(!e)return f.error("用户ID无效"),void goBack();Q.value=!0;try{const a=await B(e);A.id=a.id,A.username=a.username,A.name=a.name,A.email=a.email,A.phone=a.phone||"",A.role=a.role,A.status=a.status,A.avatar=a.avatar||"",A.permissions=a.permissions||[]}catch(a){f.error("获取用户详情失败")}finally{Q.value=!1}},handleAvatarSuccess=e=>{A.avatar=e.data.url},beforeAvatarUpload=e=>{const a="image/jpeg"===e.type||"image/png"===e.type,l=e.size/1024/1024<2;return a||f.error("上传头像图片只能是 JPG 或 PNG 格式!"),l||f.error("上传头像图片大小不能超过 2MB!"),a&&l},submitForm=async()=>{M.value&&await M.value.validate((async e=>{if(e){S.value=!0;try{const e={name:A.name,email:A.email,phone:A.phone,role:A.role,status:A.status,avatar:A.avatar,permissions:A.permissions};await D(A.id,e),f.success("用户更新成功"),a.push("/user/list")}catch(l){f.error("更新用户失败")}finally{S.value=!1}}else f.error("请完善表单信息")}))},resetForm=()=>{getUserData()},showResetPasswordDialog=()=>{K.password="",K.confirmPassword="",W.value=!0},confirmResetPassword=async()=>{O.value&&await O.value.validate((async e=>{if(e){$.value=!0;try{await N(A.id,K.password),f.success("密码重置成功"),W.value=!1}catch(a){f.error("密码重置失败")}finally{$.value=!1}}}))},goBack=()=>{a.push("/user/list")};return r((()=>{getUserData()})),(e,a)=>{const l=h,s=_,r=V,c=y,f=k,g=j,B=U,D=P,N=C,H=I,X=v,Y=n,Z=m;return G(),o("div",R,[t(i(J),{title:"编辑用户"},{actions:d((()=>[t(s,{onClick:goBack},{default:d((()=>[t(l,null,{default:d((()=>[t(i(b))])),_:1}),a[10]||(a[10]=w("返回列表 "))])),_:1})])),_:1}),u((G(),p(X,{class:"form-card"},{default:d((()=>[t(H,{ref_key:"formRef",ref:M,model:A,rules:L,"label-width":"100px","status-icon":""},{default:d((()=>[t(c,{label:"用户名",prop:"username"},{default:d((()=>[t(r,{modelValue:A.username,"onUpdate:modelValue":a[0]||(a[0]=e=>A.username=e),placeholder:"请输入用户名",disabled:!0},null,8,["modelValue"])])),_:1}),t(c,{label:"姓名",prop:"name"},{default:d((()=>[t(r,{modelValue:A.name,"onUpdate:modelValue":a[1]||(a[1]=e=>A.name=e),placeholder:"请输入姓名"},null,8,["modelValue"])])),_:1}),t(c,{label:"邮箱",prop:"email"},{default:d((()=>[t(r,{modelValue:A.email,"onUpdate:modelValue":a[2]||(a[2]=e=>A.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1}),t(c,{label:"手机号",prop:"phone"},{default:d((()=>[t(r,{modelValue:A.phone,"onUpdate:modelValue":a[3]||(a[3]=e=>A.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])])),_:1}),t(c,{label:"角色",prop:"role"},{default:d((()=>[t(g,{modelValue:A.role,"onUpdate:modelValue":a[4]||(a[4]=e=>A.role=e),placeholder:"请选择角色"},{default:d((()=>[t(f,{label:"管理员",value:"admin"}),t(f,{label:"普通用户",value:"user"}),t(f,{label:"访客",value:"guest"})])),_:1},8,["modelValue"])])),_:1}),t(c,{label:"状态",prop:"status"},{default:d((()=>[t(D,{modelValue:A.status,"onUpdate:modelValue":a[5]||(a[5]=e=>A.status=e)},{default:d((()=>[t(B,{label:"active"},{default:d((()=>a[11]||(a[11]=[w("启用")]))),_:1}),t(B,{label:"inactive"},{default:d((()=>a[12]||(a[12]=[w("禁用")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),t(c,{label:"头像",prop:"avatar"},{default:d((()=>[t(N,{class:"avatar-uploader",action:"/api/upload","show-file-list":!1,"on-success":handleAvatarSuccess,"before-upload":beforeAvatarUpload},{default:d((()=>[A.avatar?(G(),o("img",{key:0,src:A.avatar,class:"avatar"},null,8,E)):(G(),p(l,{key:1,class:"avatar-uploader-icon"},{default:d((()=>[t(i(q))])),_:1}))])),_:1})])),_:1}),t(c,{label:"重置密码"},{default:d((()=>[t(s,{type:"warning",onClick:showResetPasswordDialog},{default:d((()=>[t(l,null,{default:d((()=>[t(i(x))])),_:1}),a[13]||(a[13]=w("重置密码 "))])),_:1})])),_:1}),t(c,null,{default:d((()=>[t(s,{type:"primary",onClick:submitForm,loading:S.value},{default:d((()=>a[14]||(a[14]=[w(" 保存 ")]))),_:1},8,["loading"]),t(s,{onClick:resetForm},{default:d((()=>a[15]||(a[15]=[w("重置")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})),[[Z,Q.value]]),t(Y,{modelValue:W.value,"onUpdate:modelValue":a[9]||(a[9]=e=>W.value=e),title:"重置密码",width:"500px"},{footer:d((()=>[z("span",F,[t(s,{onClick:a[8]||(a[8]=e=>W.value=!1)},{default:d((()=>a[16]||(a[16]=[w("取消")]))),_:1}),t(s,{type:"primary",onClick:confirmResetPassword,loading:$.value},{default:d((()=>a[17]||(a[17]=[w(" 确认 ")]))),_:1},8,["loading"])])])),default:d((()=>[t(H,{ref_key:"passwordFormRef",ref:O,model:K,rules:T,"label-width":"100px"},{default:d((()=>[t(c,{label:"新密码",prop:"password"},{default:d((()=>[t(r,{modelValue:K.password,"onUpdate:modelValue":a[6]||(a[6]=e=>K.password=e),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])])),_:1}),t(c,{label:"确认密码",prop:"confirmPassword"},{default:d((()=>[t(r,{modelValue:K.confirmPassword,"onUpdate:modelValue":a[7]||(a[7]=e=>K.confirmPassword=e),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-23fce585"]]);export{H as default};
