import{a as e}from"./index-cttjCPxy.js";/* empty css                      *//* empty css                *//* empty css                     *//* empty css                *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                 */import{e as a,a as l,f as s,Q as t,g as r,i as o,k as i,ai as d,w as u,m,aj as n,c,J as p,a5 as f,a0 as _,D as y,V as b,W as g,a6 as v,aG as h,aL as j,S as k,u as V,o as w}from"./vue-element-BXsXg3SF.js";import{P as x}from"./OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js";/* empty css                  *//* empty css                        *//* empty css                 */import{b as C,a as q,u as U}from"./role-CoSkS4Zr.js";import"./utils-OxSuZc4o.js";import"./http-CMGX6FrQ.js";const I={class:"role-edit-container"},z=e(a({__name:"edit",setup(e){const a=V(),z=s(),D=l(),J=l(),K=l(Number(z.params.id)||0),P=l(!1),R=l(!1),B=t({name:"",code:"",status:"active",description:"",permissions:[]}),G=t({name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],code:[{required:!0,message:"请输入角色编码",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}),L=l([]),getDetail=async()=>{if(K.value){P.value=!0;try{const e=await C(K.value);e&&(B.name=e.name,B.code=e.code,B.status=e.status,B.description=e.description,B.permissions=e.permissions||[])}catch(e){p.error("获取角色详情失败")}finally{P.value=!1}}else p.error("角色ID不能为空")},getPermissions=async()=>{try{const e=await q();L.value=e.map((e=>({id:e.id,name:e.name,code:e.code,description:e.description})))}catch(e){p.error("获取权限列表失败")}},handlePermissionCheck=(e,a)=>{B.permissions=a.checkedKeys},submitForm=async()=>{D.value&&await D.value.validate((async(e,l)=>{if(e){R.value=!0;try{await U(K.value,B),p.success("更新成功"),a.push("/role/list")}catch(s){p.error("更新失败")}finally{R.value=!1}}}))},resetForm=()=>{getDetail()},goBack=()=>{a.push("/role/list")};return r((async()=>{await Promise.all([getPermissions(),getDetail()])})),(e,a)=>{const l=_,s=g,t=b,r=h,p=v,V=j,C=k,q=f,U=n;return w(),o("div",I,[i(m(x),{title:"编辑角色"},{actions:u((()=>[i(l,{onClick:goBack},{default:u((()=>a[4]||(a[4]=[y("返回")]))),_:1})])),_:1}),d((w(),c(q,{class:"form-card"},{default:u((()=>[i(C,{ref_key:"formRef",ref:D,model:B,rules:G,"label-width":"100px","status-icon":""},{default:u((()=>[i(t,{label:"角色名称",prop:"name"},{default:u((()=>[i(s,{modelValue:B.name,"onUpdate:modelValue":a[0]||(a[0]=e=>B.name=e),placeholder:"请输入角色名称",clearable:""},null,8,["modelValue"])])),_:1}),i(t,{label:"角色编码",prop:"code"},{default:u((()=>[i(s,{modelValue:B.code,"onUpdate:modelValue":a[1]||(a[1]=e=>B.code=e),placeholder:"请输入角色编码",clearable:""},null,8,["modelValue"])])),_:1}),i(t,{label:"状态",prop:"status"},{default:u((()=>[i(p,{modelValue:B.status,"onUpdate:modelValue":a[2]||(a[2]=e=>B.status=e)},{default:u((()=>[i(r,{label:"active"},{default:u((()=>a[5]||(a[5]=[y("启用")]))),_:1}),i(r,{label:"inactive"},{default:u((()=>a[6]||(a[6]=[y("禁用")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"描述",prop:"description"},{default:u((()=>[i(s,{modelValue:B.description,"onUpdate:modelValue":a[3]||(a[3]=e=>B.description=e),type:"textarea",rows:"4",placeholder:"请输入角色描述"},null,8,["modelValue"])])),_:1}),i(t,{label:"权限",prop:"permissions"},{default:u((()=>[i(V,{ref_key:"permissionTreeRef",ref:J,data:L.value,"show-checkbox":"","node-key":"id",props:{label:"name",children:"children"},"default-checked-keys":B.permissions,onCheck:handlePermissionCheck},null,8,["data","default-checked-keys"])])),_:1}),i(t,null,{default:u((()=>[i(l,{type:"primary",onClick:submitForm,loading:R.value},{default:u((()=>a[7]||(a[7]=[y(" 保存 ")]))),_:1},8,["loading"]),i(l,{onClick:resetForm},{default:u((()=>a[8]||(a[8]=[y("重置")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})),[[U,P.value]])])}}}),[["__scopeId","data-v-2fa56b51"]]);export{z as default};
