var e=Object.defineProperty,__publicField=(t,r,n)=>((t,r,n)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n)(t,"symbol"!=typeof r?r+"":r,n);import{v as t}from"./utils-OxSuZc4o.js";import{r}from"./index-cttjCPxy.js";import{J as n,I as a}from"./vue-element-BXsXg3SF.js";const s={baseURL:"/api",timeout:15e3,enableLogs:!1,enableCache:!0,cacheMaxAge:3e5,autoRefreshToken:!0},o="token",c="refresh_token",i="token_expiry",u="user_info",secureStore=(e,t)=>{try{localStorage.setItem(e,t)}catch(r){}},secureRetrieve=e=>{try{return localStorage.getItem(e)}catch(t){return null}},secureRemove=e=>{try{localStorage.removeItem(e)}catch(t){}},storeTokens=(e,t,r)=>{if(e&&(secureStore(o,e),t&&secureStore(c,t),r)){const e=Date.now()+1e3*r;secureStore(i,e.toString())}},storeUserInfo=e=>{if(e)try{secureStore(u,JSON.stringify(e))}catch(t){}},getToken=()=>secureRetrieve(o),refreshToken=async()=>{const e=secureRetrieve(c);if(!e)return!1;try{const r=await t.post(`${s.baseURL}/auth/refresh-token/`,{refresh_token:e});if(r.data&&r.data.access_token){const{access_token:e,refresh_token:t,expires_in:n}=r.data;return storeTokens(e,t,n),!0}return!1}catch(r){return!1}},h={showErrorMessage:!0,logError:!0,redirectOnAuthError:!0},determineErrorType=e=>{if(!e.response)return"ECONNABORTED"===e.code||e.message.includes("timeout")||e.message.includes("Network Error")?"network":"client";const t=e.response.status;return 401===t?"auth":403===t?"permission":400===t||422===t?"validation":t>=500?"server":"unknown"},handleAuthError=(e=h)=>{secureRemove(o),secureRemove(c),secureRemove(i),secureRemove(u),e.redirectOnAuthError&&a.confirm("您的登录已过期，请重新登录","登录超时",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then((()=>{r.push("/login")})).catch((()=>{}))},handleApiError=(e,t=h)=>{const r=determineErrorType(e);if(t.logError&&(e.config,e.response),"auth"===r&&handleAuthError(t),t.showErrorMessage){const t=(e=>{var t;if(null==(t=e.response)?void 0:t.data){const t=e.response.data;if("string"==typeof t)return t;if(t.message)return t.message;if(t.error)return t.error;if(t.detail)return t.detail;if(t.errors&&Array.isArray(t.errors)&&t.errors.length>0)return t.errors[0]}switch(determineErrorType(e)){case"network":return"网络错误，请检查您的网络连接";case"auth":return"认证失败，请重新登录";case"permission":return"权限不足，无法访问该资源";case"validation":return"请求数据验证失败，请检查输入";case"server":return"服务器错误，请稍后重试";case"client":return"客户端错误，请刷新页面重试";default:return"未知错误，请稍后重试"}})(e);n.error(t)}},l=new Map,generateCacheKey=e=>{const{url:t,method:r,params:n,data:a}=e;return`${r}_${t}_${n?JSON.stringify(n):""}_${a?JSON.stringify(a):""}`},getFromCache=(e,t=3e5)=>{var r;if("get"!==((null==(r=e.method)?void 0:r.toLowerCase())||""))return null;const n=e.url||"";if(n.includes("/auth/")||n.includes("/login"))return null;const a=generateCacheKey(e),s=l.get(a);return s&&((e,t)=>Date.now()-e.timestamp<t)(s,t)?s.data:null},d={autoAddToken:!0,autoRefreshToken:!0,enableCache:s.enableCache,cacheMaxAge:s.cacheMaxAge},setupRequestInterceptor=(e,t=d)=>{let r=!1,n=[];e.interceptors.request.use((async e=>{if(e.headers||(e.headers={}),t.enableCache){const r=getFromCache(e,t.cacheMaxAge);if(r)return e.headers["X-From-Cache"]="true",Promise.reject({config:e,response:{data:r},isAxiosError:!0,isCachedResponse:!0})}if(t.autoAddToken){const t=getToken();t&&(e.headers.Authorization=`Bearer ${t}`)}if(t.autoRefreshToken&&(()=>{const e=secureRetrieve(i);if(!e)return!1;try{const t=parseInt(e,10);return t-Date.now()<3e5}catch(t){return!1}})()&&!r)try{r=!0;const t=await refreshToken();if(r=!1,t){const t=getToken();t&&(e.headers.Authorization=`Bearer ${t}`),n.forEach((e=>e(t||""))),n=[]}}catch(a){r=!1}return e}),(e=>Promise.reject(e)))},setupResponseInterceptor=(e,t=d)=>{let r=!1,n=[];e.interceptors.response.use((e=>{var r,n;return"true"===(null==(r=e.config.headers)?void 0:r["X-From-Cache"])||t.enableCache&&"get"===(null==(n=e.config.method)?void 0:n.toLowerCase())&&((e,t)=>{var r;const n=(null==(r=e.method)?void 0:r.toLowerCase())||"";if("get"!==n)return;const a=e.url||"";if(a.includes("/auth/")||a.includes("/login"))return;const s=generateCacheKey(e);l.set(s,{data:t,timestamp:Date.now(),url:a,method:n})})(e.config,e.data,t.cacheMaxAge),e}),(async a=>{var s;if(a.isCachedResponse)return a.response;if(401===(null==(s=a.response)?void 0:s.status)&&t.autoRefreshToken&&!r)try{r=!0;const t=await refreshToken();if(r=!1,t&&a.config){const t=getToken();return t&&a.config.headers&&(a.config.headers.Authorization=`Bearer ${t}`),n.forEach((e=>e(t||""))),n=[],e(a.config)}}catch(o){r=!1}return handleApiError(a),Promise.reject(a)}))};const f=new class{constructor(e={}){__publicField(this,"instance"),this.instance=t.create({baseURL:s.baseURL,timeout:s.timeout,headers:{"Content-Type":"application/json",Accept:"application/json"},...e}),((e,t=d)=>{setupRequestInterceptor(e,t),setupResponseInterceptor(e,t)})(this.instance)}async get(e,t,r){try{const n=await this.instance.get(e,{...r,params:t});return this.handleResponse(n)}catch(n){throw n}}async post(e,t,r){try{const n=await this.instance.post(e,t,r);return this.handleResponse(n)}catch(n){throw n}}async put(e,t,r){try{const n=await this.instance.put(e,t,r);return this.handleResponse(n)}catch(n){throw n}}async delete(e,t){try{const r=await this.instance.delete(e,t);return this.handleResponse(r)}catch(r){throw r}}async patch(e,t,r){try{const n=await this.instance.patch(e,t,r);return this.handleResponse(n)}catch(n){throw n}}handleResponse(e){return e.data&&"object"==typeof e.data&&"data"in e.data?e.data.data:e.data}getInstance(){return this.instance}},p=f.get.bind(f),g=f.post.bind(f),m=f.put.bind(f),y=f.delete.bind(f);f.patch.bind(f);export{storeUserInfo as a,m as b,y as d,p as g,g as p,storeTokens as s};
