import{a as e}from"./index-cttjCPxy.js";/* empty css                        *//* empty css                    *//* empty css                *//* empty css                  *//* empty css                       *//* empty css                        *//* empty css               *//* empty css                       *//* empty css                 */import{i as a}from"./chart-Bv0nTMp7.js";import{e as t,a as l,g as r,a1 as s,i as d,j as i,k as n,a2 as o,w as u,a3 as p,F as c,t as m,a4 as v,a5 as y,a6 as f,a7 as h,D as g,a8 as w,a9 as b,aa as _,ab as x,ac as T,z as j,a0 as k,y as z,o as V,c as D,q as C,ad as L,l as I,m as A,ae as E,A as O}from"./vue-element-BXsXg3SF.js";import"./utils-OxSuZc4o.js";const U={class:"dashboard-container"},q={class:"page-header"},B={class:"date-selector"},P={class:"card-title"},S={class:"card-content"},W={class:"value"},F={class:"unit"},R={class:"card-footer"},G={class:"chart-header"},H={class:"chart-header"},J={class:"card-header"},K={class:"card-header"},M=e(t({__name:"index",setup(e){const t=l([]),M=[{text:"最近一周",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-6048e5),[a,e]}},{text:"最近一个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-2592e6),[a,e]}},{text:"最近三个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-7776e6),[a,e]}}],N=l([{title:"销售总额",value:"89,762",unit:"元",trend:"12.5%",trendType:"up",type:"primary"},{title:"订单总数",value:"235",unit:"笔",trend:"5.8%",trendType:"up",type:"success"},{title:"新增客户",value:"48",unit:"位",trend:"8.2%",trendType:"up",type:"warning"},{title:"充值金额",value:"56,325",unit:"元",trend:"3.6%",trendType:"down",type:"danger"}]),Q=l("month"),X=l("product"),Y=l([{id:1,type:"approval",title:"销售订单审批 - 上海电子科技有限公司",deadline:"2023-04-10"},{id:2,type:"payment",title:"待确认付款 - 北京科技有限公司",deadline:"2023-04-12"},{id:3,type:"follow",title:"客户跟进 - 广州信息技术有限公司",deadline:"2023-04-15"},{id:4,type:"report",title:"月度销售报表提交",deadline:"2023-04-30"}]),Z=l([{id:1,username:"张三",role:"销售员",avatar:"",loginTime:"2023-04-09 08:45",ip:"*************"},{id:2,username:"李四",role:"销售员",avatar:"",loginTime:"2023-04-09 09:12",ip:"*************"},{id:3,username:"王五",role:"销售员",avatar:"",loginTime:"2023-04-09 08:30",ip:"*************"},{id:4,username:"赵经理",role:"销售经理",avatar:"",loginTime:"2023-04-09 10:05",ip:"*************"},{id:5,username:"王财务",role:"财务",avatar:"",loginTime:"2023-04-09 11:20",ip:"*************"}]),getTaskTypeText=e=>({approval:"审批",payment:"付款",follow:"跟进",report:"报表"}[e]||"其他"),handleDateChange=e=>{};let $=null;let ee=null;const handleResize=()=>{null==$||$.resize(),null==ee||ee.resize()};return r((()=>{s((()=>{(()=>{const e=document.getElementById("salesTrendChart");if(!e)return;$=a(e),$.setOption({tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["销售额","订单数"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:[{type:"value",name:"销售额",axisLabel:{formatter:"{value} 元"}},{type:"value",name:"订单数",axisLabel:{formatter:"{value} 笔"}}],series:[{name:"销售额",type:"bar",data:[12500,8700,10200,13400,9800,11300,14500]},{name:"订单数",type:"line",yAxisIndex:1,data:[32,25,28,35,29,33,42]}]})})(),(()=>{const e=document.getElementById("categoryChart");if(!e)return;ee=a(e),ee.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:["会员服务","技术支持","广告服务","数据服务","其他"]},series:[{name:"销售分类",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"14",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:42e3,name:"会员服务"},{value:18e3,name:"技术支持"},{value:12e3,name:"广告服务"},{value:15e3,name:"数据服务"},{value:2762,name:"其他"}]}]})})(),window.addEventListener("resize",handleResize)}))})),(e,a)=>{const l=o,r=I,s=y,$=v,ee=p,ae=h,te=f,le=b,re=w,se=k,de=T,ie=x,ne=_,oe=z;return V(),d("div",U,[i("div",q,[a[3]||(a[3]=i("h1",{class:"page-title"},"仪表盘",-1)),i("div",B,[n(l,{modelValue:t.value,"onUpdate:modelValue":a[0]||(a[0]=e=>t.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",shortcuts:M,onChange:handleDateChange},null,8,["modelValue"])])]),n(ee,{gutter:20},{default:u((()=>[(V(!0),d(c,null,m(N.value,((e,t)=>(V(),D($,{xs:24,sm:12,md:6,key:t},{default:u((()=>[n(s,{class:C(["data-card",e.type])},{default:u((()=>[i("div",P,j(e.title),1),i("div",S,[i("span",W,j(e.value),1),i("span",F,j(e.unit),1)]),i("div",R,[i("span",{class:C(["trend",e.trendType])},["up"===e.trendType?(V(),D(r,{key:0},{default:u((()=>[n(A(E))])),_:1})):"down"===e.trendType?(V(),D(r,{key:1},{default:u((()=>[n(A(O))])),_:1})):L("",!0),g(" "+j(e.trend),1)],2),a[4]||(a[4]=i("span",{class:"compare"},"较上期",-1))])])),_:2},1032,["class"])])),_:2},1024)))),128))])),_:1}),n(ee,{gutter:20,class:"chart-row"},{default:u((()=>[n($,{xs:24,lg:16},{default:u((()=>[n(s,{class:"chart-card"},{header:u((()=>[i("div",G,[a[8]||(a[8]=i("span",null,"销售趋势",-1)),n(te,{modelValue:Q.value,"onUpdate:modelValue":a[1]||(a[1]=e=>Q.value=e),size:"small"},{default:u((()=>[n(ae,{value:"week"},{default:u((()=>a[5]||(a[5]=[g("本周")]))),_:1}),n(ae,{value:"month"},{default:u((()=>a[6]||(a[6]=[g("本月")]))),_:1}),n(ae,{value:"quarter"},{default:u((()=>a[7]||(a[7]=[g("本季度")]))),_:1})])),_:1},8,["modelValue"])])])),default:u((()=>[a[9]||(a[9]=i("div",{class:"chart-container",id:"salesTrendChart"},null,-1))])),_:1})])),_:1}),n($,{xs:24,lg:8},{default:u((()=>[n(s,{class:"chart-card"},{header:u((()=>[i("div",H,[a[10]||(a[10]=i("span",null,"销售分类",-1)),n(re,{modelValue:X.value,"onUpdate:modelValue":a[2]||(a[2]=e=>X.value=e),size:"small",style:{width:"120px"}},{default:u((()=>[n(le,{label:"按产品分类",value:"product"}),n(le,{label:"按客户类型",value:"customer"}),n(le,{label:"按销售员",value:"salesperson"})])),_:1},8,["modelValue"])])])),default:u((()=>[a[11]||(a[11]=i("div",{class:"chart-container",id:"categoryChart"},null,-1))])),_:1})])),_:1})])),_:1}),n(ee,{gutter:20,class:"table-row"},{default:u((()=>[n($,{xs:24,lg:12},{default:u((()=>[n(s,{class:"table-card"},{header:u((()=>[i("div",J,[a[13]||(a[13]=i("span",null,"待处理任务",-1)),n(se,{type:"primary",link:""},{default:u((()=>a[12]||(a[12]=[g("查看全部")]))),_:1})])])),default:u((()=>[n(ne,{data:Y.value,style:{width:"100%"}},{default:u((()=>[n(ie,{label:"任务类型",width:"100"},{default:u((({row:e})=>{return[n(de,{type:(a=e.type,{approval:"warning",payment:"success",follow:"info",report:"primary"}[a]||"")},{default:u((()=>[g(j(getTaskTypeText(e.type)),1)])),_:2},1032,["type"])];var a})),_:1}),n(ie,{prop:"title",label:"任务内容","show-overflow-tooltip":""}),n(ie,{prop:"deadline",label:"截止日期",width:"100"}),n(ie,{label:"操作",width:"120"},{default:u((({row:e})=>[n(se,{type:"primary",link:"",size:"small"},{default:u((()=>a[14]||(a[14]=[g("处理")]))),_:1}),n(se,{type:"info",link:"",size:"small"},{default:u((()=>a[15]||(a[15]=[g("详情")]))),_:1})])),_:1})])),_:1},8,["data"])])),_:1})])),_:1}),n($,{xs:24,lg:12},{default:u((()=>[n(s,{class:"table-card"},{header:u((()=>[i("div",K,[a[17]||(a[17]=i("span",null,"最近登录",-1)),n(se,{type:"primary",link:""},{default:u((()=>a[16]||(a[16]=[g("查看日志")]))),_:1})])])),default:u((()=>[n(ne,{data:Z.value,style:{width:"100%"}},{default:u((()=>[n(ie,{width:"50"},{default:u((({row:e})=>[n(oe,{size:32,src:e.avatar},{default:u((()=>[g(j(e.username.substring(0,1)),1)])),_:2},1032,["src"])])),_:1}),n(ie,{prop:"username",label:"用户",width:"100"}),n(ie,{prop:"role",label:"角色",width:"100"}),n(ie,{prop:"loginTime",label:"登录时间"}),n(ie,{prop:"ip",label:"IP地址",width:"120"})])),_:1},8,["data"])])),_:1})])),_:1})])),_:1})])}}}),[["__scopeId","data-v-de1b34a6"]]);export{M as default};
