import{_ as e,a}from"./index-cttjCPxy.js";/* empty css                     *//* empty css                    *//* empty css                 */import{e as s,a as r,Q as l,g as o,i as t,j as m,R as n,k as u,w as d,S as i,z as c,U as p,V as g,W as f,m as v,X as _,Y as b,l as w,c as h,H as x,Z as y,_ as k,$ as V,D as j,a0 as U,J as M,u as I,o as S}from"./vue-element-BXsXg3SF.js";import{p as q,s as C,a as D}from"./http-CMGX6FrQ.js";import"./utils-OxSuZc4o.js";const F={class:"login-card"},R={class:"login-footer"},Y=a(s({__name:"index",setup(a){const s=r("./background.jpg"),Y=r(),z=I(),H=l({username:"",password:"",rememberMe:!1}),J=r(!1),L=r(!1),Q={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,message:"用户名不能少于3个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码不能少于6个字符",trigger:"blur"}]},handleLogin=async()=>{Y.value&&Y.value.validate((async e=>{if(!e)return!1;L.value=!0;try{const e={username:H.username.trim(),password:H.password,remember:H.rememberMe};await async function(e){const a=await q("/auth/login",{username:e.username,password:e.password});return a.access_token&&(C(a.access_token,a.refresh_token,a.expires_in),a.user&&D(a.user)),a}(e);H.rememberMe?localStorage.setItem("rememberedUsername",H.username.trim()):localStorage.removeItem("rememberedUsername"),M.success("登录成功"),z.push("/dashboard")}catch(a){const e=a.message||"未知错误";M.error(e)}finally{L.value=!1}}))};return o((()=>{const e=localStorage.getItem("rememberedUsername");e&&(H.username=e,H.rememberMe=!0)})),(a,r)=>{const l=f,o=g,M=w,I=V,q=U,C=i;return S(),t("div",{class:"login-container",style:p({backgroundImage:"url("+s.value+")"})},[m("div",F,[r[7]||(r[7]=n('<div class="login-header" data-v-a27aa936><div class="logo" data-v-a27aa936><img src="'+e+'" alt="Logo" class="logo-img" data-v-a27aa936><h1 class="title" data-v-a27aa936>妍大网络科技</h1></div><div class="description" data-v-a27aa936>欢迎登录，请输入您的账号密码</div></div>',1)),u(C,{ref_key:"loginFormRef",ref:Y,model:H,rules:Q,class:"login-form",autocomplete:"on"},{default:d((()=>[u(o,{prop:"username"},{default:d((()=>[u(l,{modelValue:H.username,"onUpdate:modelValue":r[0]||(r[0]=e=>H.username=e),placeholder:"用户名",type:"text","prefix-icon":v(_),autocomplete:"on"},null,8,["modelValue","prefix-icon"])])),_:1}),u(o,{prop:"password"},{default:d((()=>[u(l,{modelValue:H.password,"onUpdate:modelValue":r[2]||(r[2]=e=>H.password=e),placeholder:"密码",type:J.value?"text":"password","prefix-icon":v(b),autocomplete:"on"},{suffix:d((()=>[u(M,{class:"password-icon",onClick:r[1]||(r[1]=e=>J.value=!J.value)},{default:d((()=>[(S(),h(x(J.value?v(y):v(k))))])),_:1})])),_:1},8,["modelValue","type","prefix-icon"])])),_:1}),u(o,null,{default:d((()=>[u(I,{modelValue:H.rememberMe,"onUpdate:modelValue":r[3]||(r[3]=e=>H.rememberMe=e)},{default:d((()=>r[4]||(r[4]=[j("记住我")]))),_:1},8,["modelValue"])])),_:1}),u(o,null,{default:d((()=>[u(q,{loading:L.value,type:"primary",class:"login-button",onClick:handleLogin},{default:d((()=>r[5]||(r[5]=[j(" 登录 ")]))),_:1},8,["loading"])])),_:1})])),_:1},8,["model"]),m("div",R,[m("p",null,"© "+c((new Date).getFullYear())+" 妍大网络科技",1),r[6]||(r[6]=m("p",null,"推荐使用Chrome浏览器访问",-1))])])],4)}}}),[["__scopeId","data-v-a27aa936"]]);export{Y as default};
