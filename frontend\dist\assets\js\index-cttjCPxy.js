const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/SidebarItem-DFynsdwT.js","assets/js/vue-element-BXsXg3SF.js","assets/js/utils-OxSuZc4o.js","assets/css/SidebarItem-Bgfz9cUR.css","assets/js/Breadcrumb-d7XyvIvO.js","assets/css/Breadcrumb-D5KBpXTr.css","assets/js/index-ChL8rdEl.js","assets/js/http-CMGX6FrQ.js","assets/css/index-DJuDX93b.css","assets/css/el-form-item-CKZiX9BY.css","assets/css/el-checkbox-DIPHKmvR.css","assets/css/el-input-BCtgWP8V.css","assets/js/index-C4GqTtwr.js","assets/js/chart-Bv0nTMp7.js","assets/css/index-C_ctdFCy.css","assets/css/el-table-column-CBdd4Iyj.css","assets/css/el-card-BMp62gu_.css","assets/css/el-select-Dt7emnhu.css","assets/css/el-radio-group-BzMpJalG.css","assets/css/el-radio-button-CSkroacn.css","assets/css/el-col-Ds2mGN2S.css","assets/css/el-date-picker-CpSHs9j2.css","assets/js/list-BL4g9lrJ.js","assets/css/list-oM4nCli9.css","assets/css/el-pagination-5j8kOLbt.css","assets/js/virtual-list-DT65kIF_.js","assets/js/OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js","assets/css/OptimizedChart-Bj4e9fT6.css","assets/js/DataTable-D8ihd4AP.js","assets/css/el-empty-D4ZqTl4F.css","assets/css/virtual-list-BXPVkYmG.css","assets/js/create-Dyst6Z1m.js","assets/css/create-DtXTu6FC.css","assets/css/el-progress-DBWeHy1f.css","assets/css/el-input-number-DUUPPWGj.css","assets/css/el-divider-BUtF_RGI.css","assets/js/detail-CD-uVlcs.js","assets/css/detail-BcU0GkS3.css","assets/css/el-descriptions-item-B5Z46VRV.css","assets/js/edit-BIO2D4ek.js","assets/css/edit-B-0TXZXf.css","assets/js/list--tmlVfKY.js","assets/js/SearchForm-CUtuHkZY.js","assets/js/user-C27kkjj6.js","assets/css/list-CLjWNw-1.css","assets/js/create-Dly0T_WB.js","assets/css/create-VbzqIOkA.css","assets/css/el-radio-C-xpY9Lc.css","assets/js/detail-DtOISJyl.js","assets/css/detail-C-dWbgid.css","assets/js/edit-Dfrrpn2p.js","assets/css/edit-Bk3wmWsm.css","assets/js/list-Dh0CP5hU.js","assets/js/role-CoSkS4Zr.js","assets/css/list-uC7wEwG0.css","assets/js/create-BBdqi9eL.js","assets/css/create-U2O1c5Hj.css","assets/css/el-tree-C2sTlbKd.css","assets/js/detail-CRH7ideX.js","assets/css/detail-87pV3G8_.css","assets/js/edit-QNxOexJA.js","assets/css/edit-BwQItUyJ.css","assets/js/404-BZmmNyaC.js","assets/css/404-Cce8ud8X.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,__publicField=(t,r,n)=>((t,r,n)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n)(t,"symbol"!=typeof r?r+"":r,n);import{c as t,r,o as n,d as o,a as i,b as s,e as a,u as l,f as c,g as d,h as u,i as m,j as p,k as h,w as v,E as f,l as g,m as b,n as y,p as _,q as E,s as O,F as w,t as P,v as S,x as L,y as T,z as I,A as M,B as C,C as k,D as R,G as D,T as j,K as A,H as x,I as z,J as V,L as $,M as F,N as H,O as N,P as B}from"./vue-element-BXsXg3SF.js";import"./utils-OxSuZc4o.js";function normalizeOptions(e,t){var r;return e="object"==typeof(r=e)&&null!==r?e:Object.create(null),new Proxy(e,{get:(e,r,n)=>"key"===r?Reflect.get(e,r,n):Reflect.get(e,r,n)||Reflect.get(t,r,n)})}function hydrateStore(e,{storage:t,serializer:r,key:n,debug:o}){try{const o=null==t?void 0:t.getItem(n);o&&e.$patch(null==r?void 0:r.deserialize(o))}catch(i){}}function persistState(e,{storage:t,serializer:r,key:n,paths:o,debug:i}){try{const i=Array.isArray(o)?function(e,t){return t.reduce(((t,r)=>{const n=r.split(".");return function(e,t,r){return t.slice(0,-1).reduce(((e,t)=>/^(__proto__)$/.test(t)?{}:e[t]=e[t]||{}),e)[t[t.length-1]]=r,e}(t,n,function(e,t){return t.reduce(((e,t)=>null==e?void 0:e[t]),e)}(e,n))}),{})}(e,o):e;t.setItem(n,r.serialize(i))}catch(s){}}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))processPreload(e);new MutationObserver((e=>{for(const t of e)if("childList"===t.type)for(const e of t.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&processPreload(e)})).observe(document,{childList:!0,subtree:!0})}function processPreload(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var J=function(e={}){return t=>{const{auto:r=!1}=e,{options:{persist:n=r},store:o,pinia:i}=t;if(!n)return;if(!(o.$id in i.state.value)){const e=i._s.get(o.$id.replace("__hot:",""));return void(e&&Promise.resolve().then((()=>e.$persist())))}const s=(Array.isArray(n)?n.map((t=>normalizeOptions(t,e))):[normalizeOptions(n,e)]).map(function(e,t){return r=>{var n;try{const{storage:o=localStorage,beforeRestore:i,afterRestore:s,serializer:a={serialize:JSON.stringify,deserialize:JSON.parse},key:l=t.$id,paths:c=null,debug:d=!1}=r;return{storage:o,beforeRestore:i,afterRestore:s,serializer:a,key:(null!=(n=e.key)?n:e=>e)("string"==typeof l?l:l(t.$id)),paths:c,debug:d}}catch(o){return r.debug,null}}}(e,o)).filter(Boolean);o.$persist=()=>{s.forEach((e=>{persistState(o.$state,e)}))},o.$hydrate=({runHooks:e=!0}={})=>{s.forEach((r=>{const{beforeRestore:n,afterRestore:i}=r;e&&(null==n||n(t)),hydrateStore(o,r),e&&(null==i||i(t))}))},s.forEach((e=>{const{beforeRestore:r,afterRestore:n}=e;null==r||r(t),hydrateStore(o,e),null==n||n(t),o.$subscribe(((t,r)=>{persistState(r,e)}),{detached:!0})}))}}();const _export_sfc=(e,t)=>{const r=e.__vccOpts||e;for(const[n,o]of t)r[n]=o;return r};const W=_export_sfc({},[["render",function(e,o){const i=r("router-view");return n(),t(i)}],["__scopeId","data-v-ec67ee37"]]),U={},__vitePreload=function(e,t,r){let n=Promise.resolve();if(t&&t.length>0){let allSettled2=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const e=document.querySelector("meta[property=csp-nonce]"),r=(null==e?void 0:e.nonce)||(null==e?void 0:e.getAttribute("nonce"));n=allSettled2(t.map((e=>{if((e=function(e){return"/fengxudongdemo/"+e}(e))in U)return;U[e]=!0;const t=e.endsWith(".css"),n=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${n}`))return;const o=document.createElement("link");return o.rel=t?"stylesheet":"modulepreload",t||(o.as="script"),o.crossOrigin="",o.href=e,r&&o.setAttribute("nonce",r),document.head.appendChild(o),t?new Promise(((t,r)=>{o.addEventListener("load",t),o.addEventListener("error",(()=>r(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function handlePreloadError(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return n.then((t=>{for(const e of t||[])"rejected"===e.status&&handlePreloadError(e.reason);return e().catch(handlePreloadError)}))},q="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='200'%20height='200'%20viewBox='0%200%20200%20200'%3e%3c!--%20Blue%20arc%20shape%20--%3e%3cpath%20d='M120,30%20C170,30%20200,80%20200,130%20C200,180%20170,200%20120,200%20C70,200%2040,180%2040,130%20C40,80%2070,30%20120,30%20Z'%20fill='%230a2a8a'/%3e%3c!--%20Yellow%20letter%20'D'%20--%3e%3cpath%20d='M60,75%20C80,75%2090,90%2090,120%20C90,150%2080,165%2060,165%20L80,165%20L80,75%20L60,75%20Z'%20fill='%23fdb913'/%3e%3c/svg%3e",Y={theme:"light",layout:"vertical",sidebarCollapsed:!1,showBreadcrumb:!0,showTags:!0,fixedHeader:!0,showFooter:!0,colorPrimary:"#409EFF",fontSize:14,developerMode:!1},saveSettings=e=>{localStorage.setItem("appSettings",JSON.stringify(e))},K=o("settings",(()=>{const e=i((()=>{const e=localStorage.getItem("appSettings");if(e)try{return JSON.parse(e)}catch(t){}return{...Y}})()),t=s((()=>"system"===e.value.theme?window.matchMedia("(prefers-color-scheme: dark)").matches:"dark"===e.value.theme));function applyTheme(){const r=t.value;document.documentElement.classList.toggle("dark",r);const n=document.documentElement;r?n.setAttribute("data-theme","dark"):n.setAttribute("data-theme","light"),n.style.setProperty("--el-color-primary",e.value.colorPrimary),n.style.setProperty("--el-font-size-base",`${e.value.fontSize}px`)}return{settings:e,isDarkMode:t,setTheme:function(t){e.value.theme=t,applyTheme(),saveSettings(e.value)},setLayout:function(t){e.value.layout=t,saveSettings(e.value)},toggleSidebar:function(){e.value.sidebarCollapsed=!e.value.sidebarCollapsed,saveSettings(e.value)},setSidebarCollapsed:function(t){e.value.sidebarCollapsed=t,saveSettings(e.value)},setPrimaryColor:function(t){e.value.colorPrimary=t,document.documentElement.style.setProperty("--el-color-primary",t),saveSettings(e.value)},setFontSize:function(t){e.value.fontSize=t,document.documentElement.style.setProperty("--el-font-size-base",`${t}px`),saveSettings(e.value)},resetSettings:function(){e.value={...Y},applyTheme(),saveSettings(e.value)},applyTheme:applyTheme,init:function(){if(applyTheme(),"system"===e.value.theme){window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",applyTheme)}},toggleDeveloperMode:function(){e.value.developerMode=!e.value.developerMode,saveSettings(e.value)},setDeveloperMode:function(t){e.value.developerMode=t,saveSettings(e.value)}}})),Z={class:"sidebar-container"},G={class:"main-container"},Q={class:"navbar"},X={class:"left-area"},ee={class:"right-menu"},te={class:"user-dropdown-link"},re={class:"user-name"},ne={class:"app-main"},oe=_export_sfc(a({__name:"index",setup(e){const o=y((()=>__vitePreload((()=>import("./SidebarItem-DFynsdwT.js")),__vite__mapDeps([0,1,2,3])))),a=y((()=>__vitePreload((()=>import("./Breadcrumb-d7XyvIvO.js")),__vite__mapDeps([4,1,2,5])))),$=l(),F=c(),H=K(),N=i(!H.settings.sidebarCollapsed),B=i(!1);i(!1);const J=s((()=>$.options.routes.filter((e=>{var t,r;return!(null==(t=e.meta)?void 0:t.hidden)&&(null==(r=e.meta)?void 0:r.title)&&e.path&&(Array.isArray(e.children)&&e.children.length>0||e.component)})))),W=s((()=>{const{meta:e,path:t}=F;return e.activeMenu?e.activeMenu:t})),U=s((()=>{const e=localStorage.getItem("user_info");if(e)try{const t=JSON.parse(e);return t.name||t.username||"未登录"}catch(t){}return localStorage.getItem("userName")||"未登录"}));s((()=>{const e=localStorage.getItem("user_info");if(e)try{const t=JSON.parse(e);return 1===t.role_id||2===t.role_id}catch(r){}const t=localStorage.getItem("userRole");return"admin"===t||"superadmin"===t})),s((()=>H.settings.developerMode));const toggleSidebar=()=>{N.value=!N.value,H.setSidebarCollapsed(!N.value),setTimeout((()=>{window.dispatchEvent(new Event("resize"))}),300)},handleLogout=()=>{z.confirm("确定要退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{localStorage.removeItem("token"),localStorage.removeItem("userName"),localStorage.removeItem("userId"),$.push("/login"),V.success("退出登录成功")})).catch((()=>{}))},handleResize=()=>{const e=B.value;B.value=window.innerWidth<992,e!==B.value&&B.value&&(N.value=!1,H.setSidebarCollapsed(!0))},handleSelect=(e,t)=>{if(!e||"string"!=typeof e)return;t.filter((e=>""!==e));if(e!==$.currentRoute.value.path)try{$.push(e).then((()=>{})).catch((e=>{}))}catch(r){}};return d((()=>{N.value=!H.settings.sidebarCollapsed,handleResize(),window.addEventListener("resize",handleResize),setTimeout((()=>{window.dispatchEvent(new Event("resize"))}),300)})),u((()=>{window.removeEventListener("resize",handleResize)})),(e,i)=>{const s=O,l=f,c=g,d=T,u=k,y=C,z=_,V=r("router-view");return n(),m("div",{class:E(["app-wrapper",{mobile:B.value,"hide-sidebar":!N.value}])},[p("div",Z,[i[0]||(i[0]=p("div",{class:"logo-container"},[p("img",{class:"logo-img",src:q,alt:"Logo"}),p("h1",{class:"logo-title"},"妍大网络科技")],-1)),h(l,null,{default:v((()=>[h(s,{"default-active":W.value,"background-color":"#001529","text-color":"#fff","active-text-color":"#409EFF",collapse:!N.value,"unique-opened":!0,router:!1,onSelect:handleSelect},{default:v((()=>[(n(!0),m(w,null,P(J.value,(e=>(n(),t(b(o),{key:e.path,item:e,"base-path":e.path},null,8,["item","base-path"])))),128))])),_:1},8,["default-active","collapse"])])),_:1})]),p("div",G,[p("div",Q,[p("div",X,[h(c,{class:"toggle-sidebar",onClick:toggleSidebar},{default:v((()=>[N.value?(n(),t(b(L),{key:1})):(n(),t(b(S),{key:0}))])),_:1}),h(b(a))]),p("div",ee,[h(z,{trigger:"click"},{dropdown:v((()=>[h(y,null,{default:v((()=>[h(u,{divided:"",onClick:handleLogout},{default:v((()=>[h(c,null,{default:v((()=>[h(b(D))])),_:1}),i[1]||(i[1]=R(" 退出登录 "))])),_:1})])),_:1})])),default:v((()=>[p("div",te,[h(d,{size:32,icon:"UserFilled"}),p("span",re,I(U.value),1),h(c,null,{default:v((()=>[h(b(M))])),_:1})])])),_:1})])]),p("div",ne,[h(l,null,{default:v((()=>[h(V,null,{default:v((({Component:e})=>[h(j,{name:"fade-transform",mode:"out-in"},{default:v((()=>[(n(),t(A,null,[(n(),t(x(e)))],1024))])),_:2},1024)])),_:1})])),_:1})])])],2)}}}),[["__scopeId","data-v-20883149"]]),ie=[{path:"/login",name:"Login",component:()=>__vitePreload((()=>import("./index-ChL8rdEl.js")),__vite__mapDeps([6,1,2,7,8,9,10,11])),meta:{title:"登录",public:!0,hidden:!0}},{path:"/",component:oe,redirect:"/dashboard",meta:{title:"首页"},children:[{path:"dashboard",name:"Dashboard",component:()=>__vitePreload((()=>import("./index-C4GqTtwr.js")),__vite__mapDeps([12,13,1,2,14,15,10,16,17,18,19,20,21,11])),meta:{title:"首页",icon:"Odometer"}}]},{path:"/sales",component:oe,name:"Sales",redirect:"/sales/list",meta:{title:"销售管理",icon:"Sell"},children:[{path:"list",name:"SalesList",component:()=>__vitePreload((()=>import("./list-BL4g9lrJ.js")),__vite__mapDeps([22,1,2,7,23,24,16,17,11,15,10,9,21])),meta:{title:"销售列表",icon:"List"}},{path:"virtual-list",name:"VirtualList",component:()=>__vitePreload((()=>import("./virtual-list-DT65kIF_.js")),__vite__mapDeps([25,1,2,26,27,28,24,16,17,11,15,10,29,30,18,19,9])),meta:{title:"虚拟滚动列表",icon:"DataLine"}},{path:"create",name:"CreateSales",component:()=>__vitePreload((()=>import("./create-Dyst6Z1m.js")),__vite__mapDeps([31,1,2,32,16,9,33,11,34,20,17,21,35])),meta:{title:"创建销售",icon:"Plus"}},{path:"detail/:id",name:"SalesDetail",component:()=>__vitePreload((()=>import("./detail-CD-uVlcs.js")),__vite__mapDeps([36,1,2,37,16,29,15,10,35,38])),meta:{title:"销售详情",icon:"InfoFilled",hidden:!0}},{path:"edit/:id",name:"EditSales",component:()=>__vitePreload((()=>import("./edit-BIO2D4ek.js")),__vite__mapDeps([39,1,2,40,16,9,11,34,35,20,17,21])),meta:{title:"编辑销售",icon:"Edit",hidden:!0}}]},{path:"/user",component:oe,name:"User",redirect:"/user/list",meta:{title:"用户管理"},children:[{path:"list",name:"UserList",component:()=>__vitePreload((()=>import("./list--tmlVfKY.js")),__vite__mapDeps([41,1,2,26,27,42,16,9,28,24,17,11,15,10,29,43,7,44,21])),meta:{title:"用户列表",icon:"List"}},{path:"create",name:"CreateUser",component:()=>__vitePreload((()=>import("./create-Dly0T_WB.js")),__vite__mapDeps([45,1,2,26,27,43,7,46,16,9,33,18,47,17,11,24,15,10,29])),meta:{title:"新增用户",icon:"Plus",hidden:!0}},{path:"detail/:id",name:"UserDetail",component:()=>__vitePreload((()=>import("./detail-DtOISJyl.js")),__vite__mapDeps([48,1,2,26,27,43,7,49,24,16,38,9,17,11,15,10,29])),meta:{title:"用户详情",icon:"InfoFilled",hidden:!0}},{path:"edit/:id",name:"EditUser",component:()=>__vitePreload((()=>import("./edit-Dfrrpn2p.js")),__vite__mapDeps([50,1,2,26,27,43,7,51,24,16,9,33,18,47,17,11,15,10,29])),meta:{title:"编辑用户",icon:"Edit",hidden:!0}}]},{path:"/role",component:oe,redirect:"/role/list",name:"Role",meta:{title:"角色管理",icon:"UserFilled"},children:[{path:"list",name:"RoleList",component:()=>__vitePreload((()=>import("./list-Dh0CP5hU.js")),__vite__mapDeps([52,1,2,26,27,42,16,9,28,24,17,11,15,10,29,53,7,54])),meta:{title:"角色列表",icon:"List"}},{path:"create",name:"CreateRole",component:()=>__vitePreload((()=>import("./create-BBdqi9eL.js")),__vite__mapDeps([55,1,2,26,27,53,7,56,16,9,57,10,18,47,11,24,17,15,29])),meta:{title:"新增角色",hidden:!0}},{path:"detail/:id",name:"RoleDetail",component:()=>__vitePreload((()=>import("./detail-CRH7ideX.js")),__vite__mapDeps([58,1,2,26,27,53,7,59,24,16,29,35,38,9,17,11,15,10])),meta:{title:"角色详情",hidden:!0}},{path:"edit/:id",name:"EditRole",component:()=>__vitePreload((()=>import("./edit-QNxOexJA.js")),__vite__mapDeps([60,1,2,26,27,53,7,61,24,16,9,57,10,18,47,11,17,15,29])),meta:{title:"编辑角色",hidden:!0}}]},{path:"/:pathMatch(.*)*",redirect:"/404",meta:{hidden:!0}},{path:"/404",component:()=>__vitePreload((()=>import("./404-BZmmNyaC.js")),__vite__mapDeps([62,1,2,63])),meta:{title:"404",hidden:!0,public:!0}}],se=$({history:F("/fengxudongdemo/"),routes:ie,linkActiveClass:"router-link-active",linkExactActiveClass:"router-link-exact-active",scrollBehavior:()=>({top:0})});se.beforeEach((async(e,t,r)=>{document.title=e.meta.title?`${e.meta.title} - 妍大网络科技`:"妍大网络科技";const n=localStorage.getItem("token"),o=K().settings.developerMode;if(!e.meta.requireDeveloperMode||o){if(e.meta.requireAdmin){const e=localStorage.getItem("user_info");let t=!1;if(e)try{const r=JSON.parse(e);t=1===r.role_id||2===r.role_id}catch(i){}if(!t)return void r("/dashboard")}if("/login"===e.path)n?r("/dashboard"):r();else if(0===e.matched.length)r("/dashboard");else if(e.meta.public)r();else if(n)try{if(e.matched.some((e=>!e.components||!e.components.default)))return void r("/dashboard");r()}catch(s){r("/dashboard")}else r("/login")}else r("/dashboard")}));const ae={autoCollect:!0,resourceMonitoring:!0,memoryMonitoring:!0,interactionMonitoring:!0,errorMonitoring:!0,consoleLog:!1,samplingRate:1};const le=new class{constructor(e={}){__publicField(this,"options"),__publicField(this,"metrics",{}),__publicField(this,"resourceObserver",null),__publicField(this,"paintObserver",null),__publicField(this,"layoutShiftObserver",null),__publicField(this,"largestContentfulPaintObserver",null),__publicField(this,"firstInputObserver",null),__publicField(this,"interactionObserver",null),__publicField(this,"navigationObserver",null),__publicField(this,"errorHandler",null),__publicField(this,"memoryInterval",null),this.options={...ae,...e},this.metrics={custom:{}},this.options.autoCollect&&this.startMonitoring()}startMonitoring(){window.performance&&(Math.random()>(this.options.samplingRate||1)||(this.monitorPageLoad(),this.options.resourceMonitoring&&this.monitorResources(),this.options.memoryMonitoring&&this.monitorMemory(),this.options.interactionMonitoring&&this.monitorInteractions(),this.options.errorMonitoring&&this.monitorErrors()))}stopMonitoring(){this.disconnectObservers(),null!==this.memoryInterval&&(window.clearInterval(this.memoryInterval),this.memoryInterval=null),this.errorHandler&&(window.removeEventListener("error",this.errorHandler),this.errorHandler=null)}getMetrics(){return{...this.metrics}}addCustomMetric(e,t){this.metrics.custom||(this.metrics.custom={}),this.metrics.custom[e]=t,this.options.consoleLog}report(){this.options.reportCallback&&this.options.reportCallback(this.getMetrics()),this.options.consoleLog}monitorPageLoad(){if(window.performance.timing){const e=window.performance.timing;this.metrics.pageLoadTime=e.loadEventEnd-e.navigationStart,this.metrics.firstScreenRender=e.domContentLoadedEventEnd-e.navigationStart}if(window.PerformanceObserver)try{this.paintObserver=new PerformanceObserver((e=>{e.getEntries().forEach((e=>{const t=e.name;"first-paint"===t||"first-contentful-paint"===t?this.metrics.firstContentfulPaint=e.startTime:"first-meaningful-paint"===t&&(this.metrics.firstMeaningfulPaint=e.startTime)}))})),this.paintObserver.observe({entryTypes:["paint"]}),this.largestContentfulPaintObserver=new PerformanceObserver((e=>{const t=e.getEntries(),r=t[t.length-1];r&&(this.metrics.largestContentfulPaint=r.startTime)})),this.largestContentfulPaintObserver.observe({entryTypes:["largest-contentful-paint"]}),this.layoutShiftObserver=new PerformanceObserver((e=>{let t=0;e.getEntries().forEach((e=>{e.hadRecentInput||(t+=e.value)})),this.metrics.cumulativeLayoutShift=t})),this.layoutShiftObserver.observe({entryTypes:["layout-shift"]}),this.firstInputObserver=new PerformanceObserver((e=>{const t=e.getEntries()[0];t&&(this.metrics.firstInputDelay=t.processingStart-t.startTime)})),this.firstInputObserver.observe({entryTypes:["first-input"]}),this.navigationObserver=new PerformanceObserver((e=>{const t=e.getEntries()[0];t&&(this.metrics.pageLoadTime=t.loadEventEnd-t.startTime,this.metrics.firstScreenRender=t.domContentLoadedEventEnd-t.startTime)})),this.navigationObserver.observe({entryTypes:["navigation"]})}catch(e){}}monitorResources(){if(window.PerformanceObserver)try{this.resourceObserver=new PerformanceObserver((e=>{const t=e.getEntries();this.metrics.resourceLoadTime||(this.metrics.resourceLoadTime={}),t.forEach((e=>{const t=e.name,r=e.duration;if(t.endsWith(".js")||t.endsWith(".css")||t.endsWith(".png")||t.endsWith(".jpg")||t.endsWith(".jpeg")||t.endsWith(".gif")||t.endsWith(".svg")){const e=t.substring(t.lastIndexOf("/")+1);this.metrics.resourceLoadTime[e]=r}}))})),this.resourceObserver.observe({entryTypes:["resource"]})}catch(e){}}monitorMemory(){performance.memory&&(this.memoryInterval=window.setInterval((()=>{const e=performance.memory;this.metrics.memoryUsage={jsHeapSizeLimit:e.jsHeapSizeLimit,totalJSHeapSize:e.totalJSHeapSize,usedJSHeapSize:e.usedJSHeapSize}}),1e4))}monitorInteractions(){if(window.PerformanceObserver)try{this.interactionObserver=new PerformanceObserver((e=>{e.getEntries().forEach((e=>{"longtask"===e.entryType&&e.duration>50&&this.addCustomMetric(`longtask_${Date.now()}`,e.duration)}))})),this.interactionObserver.observe({entryTypes:["longtask"]})}catch(e){}}monitorErrors(){this.errorHandler=e=>{this.addCustomMetric(`error_${Date.now()}`,1)},window.addEventListener("error",this.errorHandler)}disconnectObservers(){this.resourceObserver&&(this.resourceObserver.disconnect(),this.resourceObserver=null),this.paintObserver&&(this.paintObserver.disconnect(),this.paintObserver=null),this.layoutShiftObserver&&(this.layoutShiftObserver.disconnect(),this.layoutShiftObserver=null),this.largestContentfulPaintObserver&&(this.largestContentfulPaintObserver.disconnect(),this.largestContentfulPaintObserver=null),this.firstInputObserver&&(this.firstInputObserver.disconnect(),this.firstInputObserver=null),this.interactionObserver&&(this.interactionObserver.disconnect(),this.interactionObserver=null),this.navigationObserver&&(this.navigationObserver.disconnect(),this.navigationObserver=null)}}({consoleLog:!1});le.startMonitoring.bind(le),le.stopMonitoring.bind(le),le.getMetrics.bind(le),le.addCustomMetric.bind(le),le.report.bind(le);const ce=H(W),de=N();de.use(J),ce.use(de),ce.use(se),ce.use(B,{locale:{name:"zh-cn",el:{breadcrumb:{label:"面包屑"},colorpicker:{confirm:"确定",clear:"清空",defaultLabel:"颜色选择器",description:"当前颜色 {color}，按 Enter 键选择新颜色",alphaLabel:"选择透明度的值"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",dateTablePrompt:"使用方向键与 Enter 键可选择日期",monthTablePrompt:"使用方向键与 Enter 键可选择月份",yearTablePrompt:"使用方向键与 Enter 键可选择年份",selectedDate:"已选日期",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},weeksFull:{sun:"星期日",mon:"星期一",tue:"星期二",wed:"星期三",thu:"星期四",fri:"星期五",sat:"星期六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},inputNumber:{decrease:"减少数值",increase:"增加数值"},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},dropdown:{toggleDropdown:"切换下拉选项"},mention:{loading:"加载中"},cascader:{noMatch:"无匹配数据",loading:"加载中",placeholder:"请选择",noData:"暂无数据"},pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",page:"页",prev:"上一页",next:"下一页",currentPage:"第 {pager} 页",prevPages:"向前 {pager} 页",nextPages:"向后 {pager} 页",deprecationWarning:"你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"},dialog:{close:"关闭此对话框"},drawer:{close:"关闭此对话框"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!",close:"关闭此对话框"},upload:{deleteTip:"按 delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},slider:{defaultLabel:"滑块介于 {min} 至 {max}",defaultRangeStartLabel:"选择起始值",defaultRangeEndLabel:"选择结束值"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},tour:{next:"下一步",previous:"上一步",finish:"结束导览"},tree:{emptyText:"暂无数据"},transfer:{noMatch:"无匹配数据",noData:"无数据",titles:["列表 1","列表 2"],filterPlaceholder:"请输入搜索内容",noCheckedFormat:"共 {total} 项",hasCheckedFormat:"已选 {checked}/{total} 项"},image:{error:"加载失败"},pageHeader:{title:"返回"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"},carousel:{leftArrow:"上一张幻灯片",rightArrow:"下一张幻灯片",indicator:"幻灯片切换至索引 {index}"}}},size:"default"}),le.startMonitoring(),ce.mount("#app"),window.addEventListener("load",(()=>{setTimeout((()=>{le.report()}),1e3)}));export{q as _,_export_sfc as a,se as r,K as u};
