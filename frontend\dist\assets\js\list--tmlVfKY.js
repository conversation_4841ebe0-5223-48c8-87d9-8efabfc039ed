import{a as e}from"./index-cttjCPxy.js";/* empty css                *//* empty css                        *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                     */import{e as a,Q as l,a as t,g as r,i as o,k as s,w as n,m as i,J as u,a0 as d,D as p,l as m,af as c,V as g,W as f,a8 as h,a9 as _,a2 as v,ab as b,ac as y,z as w,u as V,I as j,o as z}from"./vue-element-BXsXg3SF.js";import{P as k}from"./OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js";import{S as C}from"./SearchForm-CUtuHkZY.js";import{D as R}from"./DataTable-D8ihd4AP.js";/* empty css                 */import{g as D,d as x}from"./user-C27kkjj6.js";import"./utils-OxSuZc4o.js";/* empty css                      */import"./http-CMGX6FrQ.js";const S={class:"user-list-container"},U=e(a({__name:"list",setup(e){const a=V(),U=l({page:1,size:10,username:"",name:"",role:"",status:"",dateRange:[]}),Y=t([]),M=t(!1),A=t(0),B=t([]),getList=async()=>{M.value=!0;try{const e={page:U.page,size:U.size,username:U.username,name:U.name,role:U.role,status:U.status};U.dateRange&&2===U.dateRange.length&&(e.start_date=U.dateRange[0],e.end_date=U.dateRange[1]);const a=await D(e);a&&a.items?(Y.value=a.items,A.value=a.total):Array.isArray(a)?(Y.value=a,A.value=a.length):(Y.value=[],A.value=0)}catch(e){u.error("获取用户数据失败"),Y.value=[],A.value=0}finally{M.value=!1}},handleSearch=()=>{U.page=1,getList()},resetQuery=()=>{U.username="",U.name="",U.role="",U.status="",U.dateRange=[],U.page=1,getList()},handleSelectionChange=e=>{B.value=e},handleSizeChange=e=>{U.size=e,getList()},handleCurrentChange=e=>{U.page=e,getList()},handleCreate=()=>{a.push("/user/create")},formatDate=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},getRoleName=e=>({admin:"管理员",user:"普通用户",guest:"访客"}[e]||e);return r((()=>{getList()})),(e,l)=>{const t=m,r=d,V=f,D=g,B=_,I=h,T=v,P=b,$=y;return z(),o("div",S,[s(i(k),{title:"用户管理"},{actions:n((()=>[s(r,{type:"primary",onClick:handleCreate},{default:n((()=>[s(t,null,{default:n((()=>[s(i(c))])),_:1}),l[8]||(l[8]=p("新增用户 "))])),_:1})])),_:1}),s(i(C),{modelValue:U,"onUpdate:modelValue":l[5]||(l[5]=e=>U=e),loading:M.value,onSearch:handleSearch,onReset:resetQuery},{default:n((()=>[s(D,{label:"用户名",prop:"username"},{default:n((()=>[s(V,{modelValue:U.username,"onUpdate:modelValue":l[0]||(l[0]=e=>U.username=e),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])])),_:1}),s(D,{label:"姓名",prop:"name"},{default:n((()=>[s(V,{modelValue:U.name,"onUpdate:modelValue":l[1]||(l[1]=e=>U.name=e),placeholder:"请输入姓名",clearable:""},null,8,["modelValue"])])),_:1}),s(D,{label:"角色",prop:"role"},{default:n((()=>[s(I,{modelValue:U.role,"onUpdate:modelValue":l[2]||(l[2]=e=>U.role=e),placeholder:"请选择角色",clearable:""},{default:n((()=>[s(B,{label:"管理员",value:"admin"}),s(B,{label:"普通用户",value:"user"}),s(B,{label:"访客",value:"guest"})])),_:1},8,["modelValue"])])),_:1}),s(D,{label:"状态",prop:"status"},{default:n((()=>[s(I,{modelValue:U.status,"onUpdate:modelValue":l[3]||(l[3]=e=>U.status=e),placeholder:"请选择状态",clearable:""},{default:n((()=>[s(B,{label:"启用",value:"active"}),s(B,{label:"禁用",value:"inactive"})])),_:1},8,["modelValue"])])),_:1}),s(D,{label:"创建时间",prop:"dateRange"},{default:n((()=>[s(T,{modelValue:U.dateRange,"onUpdate:modelValue":l[4]||(l[4]=e=>U.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1})])),_:1},8,["modelValue","loading"]),s(i(R),{data:Y.value,loading:M.value,total:A.value,"current-page":U.page,"onUpdate:currentPage":l[6]||(l[6]=e=>U.page=e),"page-size":U.size,"onUpdate:pageSize":l[7]||(l[7]=e=>U.size=e),onSizeChange:handleSizeChange,onCurrentChange:handleCurrentChange,onSelectionChange:handleSelectionChange,showSelection:"",showIndex:""},{action:n((({row:e})=>[s(r,{type:"primary",link:"",onClick:l=>(e=>{a.push(`/user/edit/${e.id}`)})(e)},{default:n((()=>l[9]||(l[9]=[p("编辑")]))),_:2},1032,["onClick"]),s(r,{type:"primary",link:"",onClick:l=>(e=>{a.push(`/user/detail/${e.id}`)})(e)},{default:n((()=>l[10]||(l[10]=[p("查看")]))),_:2},1032,["onClick"]),s(r,{type:"danger",link:"",onClick:a=>(e=>{"admin"!==e.role?j.confirm("确认删除该用户吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await x(e.id),u.success("删除成功"),getList()}catch(a){u.error("删除失败")}})).catch((()=>{u.info("已取消删除")})):u.warning("管理员账号不能删除")})(e),disabled:"admin"===e.role},{default:n((()=>l[11]||(l[11]=[p("删除")]))),_:2},1032,["onClick","disabled"])])),default:n((()=>[s(P,{prop:"username",label:"用户名","min-width":"120"}),s(P,{prop:"name",label:"姓名","min-width":"120"}),s(P,{prop:"role",label:"角色","min-width":"100"},{default:n((({row:e})=>{return[s($,{type:(a=e.role,{admin:"danger",user:"primary",guest:"info"}[a]||"info")},{default:n((()=>[p(w(getRoleName(e.role)),1)])),_:2},1032,["type"])];var a})),_:1}),s(P,{prop:"email",label:"邮箱","min-width":"180","show-overflow-tooltip":""}),s(P,{prop:"phone",label:"手机号","min-width":"120"}),s(P,{prop:"status",label:"状态","min-width":"80"},{default:n((({row:e})=>[s($,{type:"active"===e.status?"success":"danger"},{default:n((()=>[p(w("active"===e.status?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),s(P,{prop:"created_at",label:"创建时间","min-width":"150"},{default:n((({row:e})=>[p(w(formatDate(e.created_at)),1)])),_:1})])),_:1},8,["data","loading","total","current-page","page-size"])])}}}),[["__scopeId","data-v-1068c34e"]]);export{U as default};
