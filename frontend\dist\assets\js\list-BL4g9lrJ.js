import{a as e}from"./index-cttjCPxy.js";/* empty css                      *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css                     *//* empty css                       */import{e as a,a as l,Q as t,g as s,i as r,j as n,k as o,w as i,a0 as u,a5 as d,J as p,D as c,l as m,m as _,af as f,S as h,V as g,a2 as y,W as w,a8 as v,a9 as b,ag as k,ah as V,ai as j,aj as z,c as C,ab as x,z as R,ac as Y,aa as D,ak as N,u as U,I as S,o as A}from"./vue-element-BXsXg3SF.js";import{g as M,d as $}from"./http-CMGX6FrQ.js";import"./utils-OxSuZc4o.js";const F={class:"sales-list-container"},q={class:"page-header"},B={class:"header-actions"},I={class:"pagination-container"},T=e(a({__name:"list",setup(e){const a=U(),T=l(),J=t({page:1,size:10,dateRange:[],sales_person:"",customer_name:"",product_name:"",audit_status:""}),L=l(!1),P=l(0),Q=l([]),W=l([]),getList=async()=>{L.value=!0;try{const e={page:J.page,size:J.size};J.dateRange&&2===J.dateRange.length&&(e.start_date=J.dateRange[0],e.end_date=J.dateRange[1]),J.sales_person&&(e.sales_person=J.sales_person),J.customer_name&&(e.customer_name=J.customer_name),J.product_name&&(e.product_name=J.product_name),J.audit_status&&(e.audit_status=J.audit_status);const a=await async function(e){return M("/sales/",e)}(e);a&&a.items&&Array.isArray(a.items)?(Q.value=a.items,P.value=a.total||a.items.length||0):Array.isArray(a)?(Q.value=a,P.value=a.length):(Q.value=[],P.value=0)}catch(e){p.error("获取销售数据失败"),Q.value=[],P.value=0}finally{L.value=!1}},handleSearch=()=>{J.page=1,getList()},resetQuery=()=>{T.value&&T.value.resetFields(),handleSearch()},handleSelectionChange=e=>{W.value=e},handleSizeChange=e=>{J.size=e,getList()},handleCurrentChange=e=>{J.page=e,getList()},handleCreate=()=>{a.push("/sales/create")},handleDelete=e=>{S.confirm("确认删除该销售记录吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await async function(e){return $(`/sales/${e}/`)}(e.id),p.success("删除成功"),getList()}catch(a){p.error("删除失败")}})).catch((()=>{p.info("已取消删除")}))},formatPrice=e=>`¥ ${e.toFixed(2)}`,formatDate=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN")};return s((()=>{getList()})),(e,l)=>{const t=m,s=u,p=y,U=g,S=w,M=b,$=v,W=h,E=d,G=x,H=Y,K=D,O=N,X=z;return A(),r("div",F,[n("div",q,[l[8]||(l[8]=n("h1",{class:"page-title"},"销售管理",-1)),n("div",B,[o(s,{type:"primary",onClick:handleCreate},{default:i((()=>[o(t,null,{default:i((()=>[o(_(f))])),_:1}),l[7]||(l[7]=c("新增销售 "))])),_:1})])]),o(E,{class:"filter-container"},{default:i((()=>[o(W,{model:J,ref_key:"queryForm",ref:T,inline:!0},{default:i((()=>[o(U,{label:"转账时间",prop:"dateRange"},{default:i((()=>[o(p,{modelValue:J.dateRange,"onUpdate:modelValue":l[0]||(l[0]=e=>J.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),o(U,{label:"负责人",prop:"salesperson"},{default:i((()=>[o(S,{modelValue:J.salesperson,"onUpdate:modelValue":l[1]||(l[1]=e=>J.salesperson=e),placeholder:"请输入",clearable:"",style:{width:"150px"}},null,8,["modelValue"])])),_:1}),o(U,{label:"转入方账户",prop:"customerName"},{default:i((()=>[o(S,{modelValue:J.customerName,"onUpdate:modelValue":l[2]||(l[2]=e=>J.customerName=e),placeholder:"请输入",clearable:"",style:{width:"150px"}},null,8,["modelValue"])])),_:1}),o(U,{label:"渠道/销售名称",prop:"channelName"},{default:i((()=>[o(S,{modelValue:J.channelName,"onUpdate:modelValue":l[3]||(l[3]=e=>J.channelName=e),placeholder:"请输入",clearable:"",style:{width:"150px"}},null,8,["modelValue"])])),_:1}),o(U,{label:"稽核状态",prop:"status"},{default:i((()=>[o($,{modelValue:J.status,"onUpdate:modelValue":l[4]||(l[4]=e=>J.status=e),placeholder:"全部",clearable:"",style:{width:"150px"}},{default:i((()=>[o(M,{label:"未稽核",value:"未稽核"}),o(M,{label:"已匹配",value:"已匹配"}),o(M,{label:"未匹配",value:"未匹配"})])),_:1},8,["modelValue"])])),_:1}),o(U,null,{default:i((()=>[o(s,{type:"primary",onClick:handleSearch},{default:i((()=>[o(t,null,{default:i((()=>[o(_(k))])),_:1}),l[9]||(l[9]=c("搜索 "))])),_:1}),o(s,{onClick:resetQuery},{default:i((()=>[o(t,null,{default:i((()=>[o(_(V))])),_:1}),l[10]||(l[10]=c("重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),o(E,{class:"table-container"},{default:i((()=>[j((A(),C(K,{data:Q.value,border:"",stripe:"",onSelectionChange:handleSelectionChange,style:{width:"100%"}},{default:i((()=>[o(G,{type:"selection",width:"55",align:"center"}),o(G,{prop:"transfer_time",label:"转账时间","min-width":"100"},{default:i((({row:e})=>[c(R(formatDate(e.transfer_time)),1)])),_:1}),o(G,{prop:"recipient_account_name",label:"转入方账户","min-width":"150","show-overflow-tooltip":""}),o(G,{prop:"channel_sales_name",label:"渠道/销售名称","min-width":"150","show-overflow-tooltip":""}),o(G,{prop:"person_in_charge",label:"负责人","min-width":"90"}),o(G,{prop:"business_platform",label:"业务平台","min-width":"120"}),o(G,{prop:"currency",label:"户币","min-width":"100",align:"right"},{default:i((({row:e})=>[c(R(formatPrice(e.currency)),1)])),_:1}),o(G,{prop:"profit",label:"利润","min-width":"100",align:"right"},{default:i((({row:e})=>[c(R(formatPrice(e.profit)),1)])),_:1}),o(G,{prop:"audit_status",label:"稽核状态","min-width":"100"},{default:i((({row:e})=>{return[o(H,{type:(a=e.audit_status,{"未稽核":"info","已匹配":"success","未匹配":"danger"}[a]||"info")},{default:i((()=>[c(R(e.audit_status),1)])),_:2},1032,["type"])];var a})),_:1}),o(G,{prop:"remark",label:"备注","min-width":"150","show-overflow-tooltip":""}),o(G,{label:"操作",fixed:"right",width:"180"},{default:i((({row:e})=>[o(s,{type:"primary",link:"",onClick:l=>(e=>{a.push(`/sales/edit/${e.id}`)})(e)},{default:i((()=>l[11]||(l[11]=[c("编辑")]))),_:2},1032,["onClick"]),o(s,{type:"primary",link:"",onClick:l=>(e=>{a.push(`/sales/detail/${e.id}`)})(e)},{default:i((()=>l[12]||(l[12]=[c("查看")]))),_:2},1032,["onClick"]),o(s,{type:"danger",link:"",onClick:a=>handleDelete(e)},{default:i((()=>l[13]||(l[13]=[c("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[X,L.value]]),n("div",I,[o(O,{"current-page":J.page,"onUpdate:currentPage":l[5]||(l[5]=e=>J.page=e),"page-size":J.size,"onUpdate:pageSize":l[6]||(l[6]=e=>J.size=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:P.value,onSizeChange:handleSizeChange,onCurrentChange:handleCurrentChange},null,8,["current-page","page-size","total"])])])),_:1})])}}}),[["__scopeId","data-v-ed8e8288"]]);export{T as default};
