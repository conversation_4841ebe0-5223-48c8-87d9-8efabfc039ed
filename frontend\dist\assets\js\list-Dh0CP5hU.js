import{a as e}from"./index-cttjCPxy.js";/* empty css                *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css                     *//* empty css                 */import{e as a,Q as t,a as l,g as o,i,k as s,w as d,m as n,J as r,a0 as c,D as u,l as p,af as m,V as v,W as _,a8 as f,a9 as g,ab as h,ac as w,z as y,u as b,I as j,o as T}from"./vue-element-BXsXg3SF.js";import{P as k}from"./OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js";import{S as C}from"./SearchForm-CUtuHkZY.js";import{D as V}from"./DataTable-D8ihd4AP.js";/* empty css                 */import{g as z,d as Z}from"./role-CoSkS4Zr.js";import"./utils-OxSuZc4o.js";/* empty css                      */import"./http-CMGX6FrQ.js";const S={class:"role-list-container"},U=e(a({__name:"list",setup(e){const formatDate=e=>{if(!e)return"";return new Date(e).toLocaleString()},a=b(),U=t({page:1,size:10,name:void 0,code:void 0,status:void 0}),x=l([]),A=l(!1),D=l(0),I=l([]),E=[{id:1,name:"超级管理员",code:"SUPER_ADMIN",description:"系统超级管理员，拥有所有权限",status:"active",created_at:"2023-01-01T00:00:00Z",updated_at:"2023-01-01T00:00:00Z"},{id:2,name:"普通管理员",code:"ADMIN",description:"系统管理员，拥有大部分管理权限",status:"active",created_at:"2023-01-02T00:00:00Z",updated_at:"2023-01-02T00:00:00Z"},{id:3,name:"运营人员",code:"OPERATOR",description:"运营人员，负责日常运营工作",status:"active",created_at:"2023-01-03T00:00:00Z",updated_at:"2023-01-03T00:00:00Z"},{id:4,name:"财务人员",code:"FINANCE",description:"财务人员，负责财务相关工作",status:"active",created_at:"2023-01-04T00:00:00Z",updated_at:"2023-01-04T00:00:00Z"},{id:5,name:"普通用户",code:"USER",description:"普通用户，只有基本权限",status:"active",created_at:"2023-01-05T00:00:00Z",updated_at:"2023-01-05T00:00:00Z"},{id:6,name:"访客",code:"GUEST",description:"访客用户，只有查看权限",status:"inactive",created_at:"2023-01-06T00:00:00Z",updated_at:"2023-01-06T00:00:00Z"}],getList=async()=>{A.value=!0;try{const e=await z(U);if(e&&e.items)x.value=e.items,D.value=e.total;else if(Array.isArray(e))x.value=e,D.value=e.length;else{const e=filterMockData(E);x.value=e,D.value=e.length}}catch(e){r.warning("获取接口数据失败，使用模拟数据");const a=filterMockData(E);x.value=a,D.value=a.length}finally{A.value=!1}},filterMockData=e=>e.filter((e=>!(U.name&&!e.name.includes(U.name))&&(!(U.code&&!e.code.includes(U.code))&&(!U.status||e.status===U.status)))),handleSearch=()=>{U.page=1,getList()},resetQuery=()=>{Object.keys(U).forEach((e=>{"page"!==e&&"size"!==e&&(U[e]=void 0)})),U.page=1,getList()},handleSelectionChange=e=>{I.value=e},handleSizeChange=e=>{U.size=e,getList()},handleCurrentChange=e=>{U.page=e,getList()},handleCreate=()=>{a.push("/role/create")};return o((()=>{getList()})),(e,t)=>{const l=p,o=c,b=_,z=v,I=g,R=f,N=h,O=w;return T(),i("div",S,[s(n(k),{title:"角色管理"},{actions:d((()=>[s(o,{type:"primary",onClick:handleCreate},{default:d((()=>[s(l,null,{default:d((()=>[s(n(m))])),_:1}),t[6]||(t[6]=u("新增 "))])),_:1})])),_:1}),s(n(C),{modelValue:U,"onUpdate:modelValue":t[3]||(t[3]=e=>U=e),loading:A.value,onSearch:handleSearch,onReset:resetQuery},{default:d((()=>[s(z,{label:"角色名称",prop:"name"},{default:d((()=>[s(b,{modelValue:U.name,"onUpdate:modelValue":t[0]||(t[0]=e=>U.name=e),placeholder:"请输入角色名称",clearable:""},null,8,["modelValue"])])),_:1}),s(z,{label:"角色编码",prop:"code"},{default:d((()=>[s(b,{modelValue:U.code,"onUpdate:modelValue":t[1]||(t[1]=e=>U.code=e),placeholder:"请输入角色编码",clearable:""},null,8,["modelValue"])])),_:1}),s(z,{label:"状态",prop:"status"},{default:d((()=>[s(R,{modelValue:U.status,"onUpdate:modelValue":t[2]||(t[2]=e=>U.status=e),placeholder:"请选择状态",clearable:""},{default:d((()=>[s(I,{label:"启用",value:"active"}),s(I,{label:"禁用",value:"inactive"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["modelValue","loading"]),s(n(V),{data:x.value,loading:A.value,total:D.value,"current-page":U.page,"onUpdate:currentPage":t[4]||(t[4]=e=>U.page=e),"page-size":U.size,"onUpdate:pageSize":t[5]||(t[5]=e=>U.size=e),onSizeChange:handleSizeChange,onCurrentChange:handleCurrentChange,onSelectionChange:handleSelectionChange,"show-selection":""},{action:d((({row:e})=>[s(o,{type:"primary",link:"",onClick:t=>(e=>{a.push(`/role/edit/${e.id}`)})(e)},{default:d((()=>t[7]||(t[7]=[u("编辑")]))),_:2},1032,["onClick"]),s(o,{type:"primary",link:"",onClick:t=>(e=>{a.push(`/role/detail/${e.id}`)})(e)},{default:d((()=>t[8]||(t[8]=[u("查看")]))),_:2},1032,["onClick"]),s(o,{type:"danger",link:"",onClick:a=>(e=>{j.confirm("确认删除该角色吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await Z(e.id),r.success("删除成功"),getList()}catch(a){if(x.value===E||x.value.some((e=>E.find((a=>a.id===e.id))))){r.warning("删除接口调用失败，使用模拟数据进行删除");const a=E.findIndex((a=>a.id===e.id));if(-1!==a){E.splice(a,1);const e=filterMockData(E);x.value=e,D.value=e.length,r.success("模拟删除成功")}}else r.error("删除失败")}})).catch((()=>{r.info("已取消删除")}))})(e)},{default:d((()=>t[9]||(t[9]=[u("删除")]))),_:2},1032,["onClick"])])),default:d((()=>[s(N,{prop:"id",label:"ID",width:"80"}),s(N,{prop:"name",label:"角色名称","min-width":"120","show-overflow-tooltip":""}),s(N,{prop:"code",label:"角色编码","min-width":"120","show-overflow-tooltip":""}),s(N,{prop:"description",label:"描述","min-width":"180","show-overflow-tooltip":""}),s(N,{prop:"status",label:"状态",width:"100"},{default:d((({row:e})=>[s(O,{type:"active"===e.status?"success":"danger"},{default:d((()=>[u(y("active"===e.status?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),s(N,{prop:"created_at",label:"创建时间","min-width":"160"},{default:d((({row:e})=>[u(y(formatDate(e.created_at)),1)])),_:1})])),_:1},8,["data","loading","total","current-page","page-size"])])}}}),[["__scopeId","data-v-367bcce5"]]);export{U as default};
