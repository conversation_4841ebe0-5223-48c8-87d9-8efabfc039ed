import{g as s,d as a,p as r,b as t}from"./http-CMGX6FrQ.js";const getUserList=async a=>await s("/users",a),getUserDetail=async a=>await s(`/users/${a}`),createUser=async s=>await r("/users",s),updateUser=async(s,a)=>await t(`/users/${s}`,a),deleteUser=async s=>await a(`/users/${s}`),resetUserPassword=async(s,a)=>await t(`/users/${s}/reset-password`,{password:a});export{getUserDetail as a,createUser as c,deleteUser as d,getUserList as g,resetUserPassword as r,updateUser as u};
