import{a as e}from"./index-cttjCPxy.js";/* empty css                        *//* empty css                    *//* empty css                *//* empty css                  *//* empty css                       *//* empty css                        */import{e as t,a,b as l,al as i,g as o,i as n,j as s,ad as r,F as d,t as u,U as c,k as p,l as m,w as f,am as v,m as y,an as h,ao as g,o as w,q as b,z as x,c as _,H as k,D as T,ac as S,a0 as I,I as M,Q as z,ap as D,a5 as j,J as B,af as C,aq as $,a6 as N,a7 as q,a8 as P,a9 as V,ab as A}from"./vue-element-BXsXg3SF.js";import{P as H}from"./OptimizedChart.vue_vue_type_style_index_0_scoped_e0fab35a_lang-i2duvu5r.js";/* empty css                     */import{D as L}from"./DataTable-D8ihd4AP.js";/* empty css                 */import"./utils-OxSuZc4o.js";/* empty css                      *//* empty css                 */const F=["width"],U=["width"],E=["onClick"],R={key:0,class:"virtual-table-loading"},K={key:1,class:"virtual-table-empty"},O=e(t({__name:"VirtualTable",props:{data:{type:Array,required:!0,default:()=>[]},columns:{type:Array,required:!0},rowKey:{type:String,default:"id"},height:{type:Number,default:400},rowHeight:{type:Number,default:48},bufferSize:{type:Number,default:5},loading:{type:Boolean,default:!1}},emits:["row-click","scroll"],setup(e,{expose:t,emit:S}){const I=e,M=S,z=a(null),D=a(null),j=a(0),B=a(0),C=l((()=>I.data.length*I.rowHeight)),$=l((()=>{const e=Math.floor(j.value/I.rowHeight)-I.bufferSize;return Math.max(0,e)})),N=l((()=>{const e=Math.ceil(I.height/I.rowHeight),t=Math.floor(j.value/I.rowHeight)+e+I.bufferSize;return Math.min(I.data.length,t)})),q=l((()=>I.data.slice($.value,N.value))),P=l((()=>$.value*I.rowHeight)),onScroll=e=>{const t=e.target;j.value=t.scrollTop,M("scroll",{scrollTop:t.scrollTop,scrollLeft:t.scrollLeft})},calculateScrollbarWidth=()=>{if(D.value){const{offsetWidth:e,clientWidth:t}=D.value;B.value=e-t}},scrollToIndex=e=>{D.value&&(D.value.scrollTop=e*I.rowHeight)};return i((()=>I.data),(()=>{D.value&&(D.value.scrollTop=0)}),{deep:!0}),o((()=>{calculateScrollbarWidth(),window.addEventListener("resize",calculateScrollbarWidth),D.value&&(D.value.scrollTop=0)})),t({scrollToIndex:scrollToIndex,scrollToRow:e=>{const t=I.data.findIndex((t=>t[I.rowKey]===e));-1!==t&&scrollToIndex(t)}}),(t,a)=>{const l=m,i=g;return w(),n("div",{class:"virtual-table-container",ref_key:"containerRef",ref:z},[s("div",{class:"virtual-table-header",style:c({paddingRight:B.value+"px"})},[s("table",null,[s("colgroup",null,[(w(!0),n(d,null,u(e.columns,((e,t)=>(w(),n("col",{key:"col-"+t,width:e.width},null,8,F)))),128))]),s("thead",null,[s("tr",null,[(w(!0),n(d,null,u(e.columns,((e,t)=>(w(),n("th",{key:"header-"+t,class:b([e.align?"text-"+e.align:""])},x(e.title),3)))),128))])])])],4),s("div",{class:"virtual-table-body",ref_key:"scrollRef",ref:D,onScroll:onScroll,style:c({height:e.height+"px"})},[s("div",{class:"virtual-table-phantom",style:c({height:C.value+"px"})},null,4),s("table",{style:c({transform:`translateY(${P.value}px)`})},[s("colgroup",null,[(w(!0),n(d,null,u(e.columns,((e,t)=>(w(),n("col",{key:"col-"+t,width:e.width},null,8,U)))),128))]),s("tbody",null,[(w(!0),n(d,null,u(q.value,((a,l)=>(w(),n("tr",{key:e.rowKey?a[e.rowKey]:l,onClick:e=>t.$emit("row-click",a,$.value+l)},[(w(!0),n(d,null,u(e.columns,((e,i)=>(w(),n("td",{key:"cell-"+i,class:b([e.align?"text-"+e.align:""])},[e.slot?v(t.$slots,e.slot,{key:0,row:a,index:$.value+l},void 0,!0):e.render?(w(),_(k(e.render(a,$.value+l)),{key:1})):(w(),n(d,{key:2},[T(x(a[e.dataIndex]),1)],64))],2)))),128))],8,E)))),128))])],4)],36),e.loading?(w(),n("div",R,[p(l,{class:"loading-icon"},{default:f((()=>[p(y(h))])),_:1})])):r("",!0),0!==e.data.length||e.loading?r("",!0):(w(),n("div",K,[v(t.$slots,"empty",{},(()=>[p(i,{description:"暂无数据"})]),!0)]))],512)}}}),[["__scopeId","data-v-a927ac6b"]]),J=t({__name:"StatusTag",props:{status:{type:[String,Number],required:!0},statusMap:{type:Object,default:()=>({})},defaultText:{type:String,default:"未知状态"},defaultType:{type:String,default:"info"},effect:{type:String,default:"light"},size:{type:String,default:"default"},color:{type:String,default:""},hit:{type:Boolean,default:!1},disableTransitions:{type:Boolean,default:!1}},setup(e){const t=e,a=l((()=>{var e;const a=String(t.status);return(null==(e=t.statusMap[a])?void 0:e.text)||t.defaultText})),i=l((()=>{var e;const a=String(t.status);return(null==(e=t.statusMap[a])?void 0:e.type)||t.defaultType}));return(t,l)=>{const o=S;return w(),_(o,{type:i.value,effect:e.effect,size:e.size,color:e.color,hit:e.hit,"disable-transitions":e.disableTransitions},{default:f((()=>[v(t.$slots,"default",{},(()=>[T(x(a.value),1)]))])),_:3},8,["type","effect","size","color","hit","disable-transitions"])}}}),W={class:"action-buttons"},Y=e(t({__name:"ActionButtons",props:{row:{type:Object,required:!0},index:{type:Number,default:0},size:{type:String,default:"small"},showView:{type:Boolean,default:!0},viewText:{type:String,default:"查看"},viewType:{type:String,default:"primary"},viewIcon:{type:String,default:""},viewLink:{type:Boolean,default:!0},viewDisabled:{type:Boolean,default:!1},showEdit:{type:Boolean,default:!0},editText:{type:String,default:"编辑"},editType:{type:String,default:"primary"},editIcon:{type:String,default:""},editLink:{type:Boolean,default:!0},editDisabled:{type:Boolean,default:!1},showDelete:{type:Boolean,default:!0},deleteText:{type:String,default:"删除"},deleteType:{type:String,default:"danger"},deleteIcon:{type:String,default:""},deleteLink:{type:Boolean,default:!0},deleteDisabled:{type:Boolean,default:!1},deleteConfirmTitle:{type:String,default:"确认删除"},deleteConfirmMessage:{type:String,default:"确定要删除这条记录吗？"},showDeleteConfirm:{type:Boolean,default:!0}},emits:["view","edit","delete"],setup(e,{emit:t}){const a=e,l=t,handleView=()=>{l("view",a.row,a.index)},handleEdit=()=>{l("edit",a.row,a.index)},handleDelete=()=>{a.showDeleteConfirm?M.confirm(a.deleteConfirmMessage,a.deleteConfirmTitle,{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{l("delete",a.row,a.index)})).catch((()=>{})):l("delete",a.row,a.index)};return(t,a)=>{const l=m,i=I;return w(),n("div",W,[e.showView?(w(),_(i,{key:0,type:e.viewType,size:e.size,link:e.viewLink,disabled:e.viewDisabled,onClick:handleView},{default:f((()=>[e.viewIcon?(w(),_(l,{key:0},{default:f((()=>[(w(),_(k(e.viewIcon)))])),_:1})):r("",!0),T(" "+x(e.viewText),1)])),_:1},8,["type","size","link","disabled"])):r("",!0),e.showEdit?(w(),_(i,{key:1,type:e.editType,size:e.size,link:e.editLink,disabled:e.editDisabled,onClick:handleEdit},{default:f((()=>[e.editIcon?(w(),_(l,{key:0},{default:f((()=>[(w(),_(k(e.editIcon)))])),_:1})):r("",!0),T(" "+x(e.editText),1)])),_:1},8,["type","size","link","disabled"])):r("",!0),e.showDelete?(w(),_(i,{key:2,type:e.deleteType,size:e.size,link:e.deleteLink,disabled:e.deleteDisabled,onClick:handleDelete},{default:f((()=>[e.deleteIcon?(w(),_(l,{key:0},{default:f((()=>[(w(),_(k(e.deleteIcon)))])),_:1})):r("",!0),T(" "+x(e.deleteText),1)])),_:1},8,["type","size","link","disabled"])):r("",!0),v(t.$slots,"default",{},void 0,!0)])}}}),[["__scopeId","data-v-b68a424b"]]),Q={class:"virtual-list-container"},G={class:"data-info"},X={class:"info-item"},Z={class:"value"},ee={class:"info-item"},te={class:"info-item"},ae={key:0,class:"table-container"},le={key:1,class:"table-container"},ie={class:"card-header"},oe={class:"performance-metrics"},ne={class:"metric-item"},se={class:"value"},re={class:"metric-item"},de={class:"value"},ue={class:"metric-item"},ce={class:"value"},pe=e(t({__name:"virtual-list",setup(e){const t=a([]),l=a(!1),i=a("virtual"),r=a(1e4),d={approved:{text:"已审核",type:"success"},pending:{text:"待审核",type:"warning"},rejected:{text:"已驳回",type:"danger"},draft:{text:"草稿",type:"info"}},u=[{title:"ID",dataIndex:"id",width:80},{title:"日期",dataIndex:"date",width:120},{title:"客户名称",dataIndex:"customerName",width:180},{title:"产品名称",dataIndex:"productName",width:150},{title:"数量",dataIndex:"quantity",width:80,align:"right"},{title:"单价",dataIndex:"unitPrice",width:120,align:"right",render:e=>formatCurrency(e.unitPrice)},{title:"总金额",dataIndex:"totalAmount",width:120,align:"right",render:e=>formatCurrency(e.totalAmount)},{title:"状态",dataIndex:"status",width:100,slot:"status"},{title:"操作",dataIndex:"action",width:200,slot:"action"}],c=z({renderTime:0,scrollFPS:0,memoryUsage:0}),formatCurrency=e=>`¥ ${e.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,",")}`,generateData=()=>{l.value=!0,setTimeout((()=>{const e=performance.now(),a=[],i=["北京科技有限公司","上海电子科技有限公司","广州信息技术有限公司","深圳互联网科技有限公司","杭州软件有限公司"],o=["高级会员服务","技术支持服务","广告推广服务","数据分析服务","云存储服务"],n=["approved","pending","rejected","draft"],s=["zhangsan","lisi","wangwu","zhaoliu"];for(let t=1;t<=r.value;t++){const e=Math.floor(10*Math.random())+1,l=Math.floor(1e4*Math.random())/100,r=e*l;a.push({id:t,date:generateRandomDate(),salesperson:s[Math.floor(Math.random()*s.length)],customerName:i[Math.floor(Math.random()*i.length)],productName:o[Math.floor(Math.random()*o.length)],quantity:e,unitPrice:l,totalAmount:r,status:n[Math.floor(Math.random()*n.length)],remark:`测试数据 ${t}`})}t.value=a,l.value=!1;const d=performance.now();c.renderTime=Math.round(d-e),B.success(`成功生成 ${r.value} 条测试数据`)}),0)},generateRandomDate=()=>{const e=new Date(2023,0,1),t=new Date,a=new Date(e.getTime()+Math.random()*(t.getTime()-e.getTime()));return`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")}`},clearData=()=>{t.value=[],c.renderTime=0,c.scrollFPS=0,c.memoryUsage=0,B.info("数据已清空")},handleRowClick=e=>{},handleView=e=>{B.info(`查看: ${e.customerName}`)},handleEdit=e=>{B.info(`编辑: ${e.customerName}`)},handleDelete=e=>{B.success(`删除: ${e.customerName}`),t.value=t.value.filter((t=>t.id!==e.id))};let v=0,h=0,g=null;const startPerformanceTest=()=>{if(0===t.value.length)return void B.warning("请先生成测试数据");window.performance&&performance.memory&&(c.memoryUsage=Math.round(performance.memory.usedJSHeapSize/1048576)),v=0,h=performance.now(),g&&clearInterval(g),B.info("开始性能测试，将在3秒后完成");const e=document.querySelector(".virtual-table-body")||document.querySelector(".el-table__body-wrapper");if(e){let t=1,a=0;g=setInterval((()=>{a+=10*t,a>5e3?t=-1:a<0&&(t=1,a=0),e.scrollTop=a,v++}),16),setTimeout((()=>{g&&(clearInterval(g),g=null);const e=(performance.now()-h)/1e3;c.scrollFPS=Math.round(v/e),B.success("性能测试完成")}),3e3)}};return o((()=>{generateData()})),D((()=>{g&&clearInterval(g)})),(e,a)=>{const o=m,v=I,h=q,g=N,b=V,_=P,k=j,S=A;return w(),n("div",Q,[p(y(H),{title:"大数据量销售列表"},{actions:f((()=>[p(v,{type:"primary",onClick:generateData},{default:f((()=>[p(o,null,{default:f((()=>[p(y(C))])),_:1}),a[2]||(a[2]=T("生成测试数据 "))])),_:1}),p(v,{onClick:clearData},{default:f((()=>[p(o,null,{default:f((()=>[p(y($))])),_:1}),a[3]||(a[3]=T("清空数据 "))])),_:1})])),_:1}),p(k,{class:"data-info-card"},{default:f((()=>[s("div",G,[s("div",X,[a[4]||(a[4]=s("span",{class:"label"},"数据总量:",-1)),s("span",Z,x(t.value.length),1)]),s("div",ee,[a[7]||(a[7]=s("span",{class:"label"},"渲染方式:",-1)),p(g,{modelValue:i.value,"onUpdate:modelValue":a[0]||(a[0]=e=>i.value=e)},{default:f((()=>[p(h,{value:"virtual"},{default:f((()=>a[5]||(a[5]=[T("虚拟滚动")]))),_:1}),p(h,{value:"normal"},{default:f((()=>a[6]||(a[6]=[T("普通表格")]))),_:1})])),_:1},8,["modelValue"])]),s("div",te,[a[8]||(a[8]=s("span",{class:"label"},"数据量:",-1)),p(_,{modelValue:r.value,"onUpdate:modelValue":a[1]||(a[1]=e=>r.value=e),placeholder:"选择数据量"},{default:f((()=>[p(b,{label:"1,000 条",value:1e3}),p(b,{label:"10,000 条",value:1e4}),p(b,{label:"50,000 条",value:5e4}),p(b,{label:"100,000 条",value:1e5})])),_:1},8,["modelValue"])])])])),_:1}),"virtual"===i.value?(w(),n("div",ae,[p(y(O),{data:t.value,columns:u,loading:l.value,height:600,"row-height":48,onRowClick:handleRowClick},{status:f((({row:e})=>[p(y(J),{status:e.status,"status-map":d},null,8,["status"])])),action:f((({row:e,index:t})=>[p(y(Y),{row:e,index:t,onView:handleView,onEdit:handleEdit,onDelete:handleDelete},null,8,["row","index"])])),_:1},8,["data","loading"])])):(w(),n("div",le,[p(y(L),{data:t.value,loading:l.value,total:t.value.length,"current-page":1,"page-size":t.value.length,"show-pagination":!1,height:"600px"},{action:f((({row:e,index:t})=>[p(y(Y),{row:e,index:t,onView:handleView,onEdit:handleEdit,onDelete:handleDelete},null,8,["row","index"])])),default:f((()=>[p(S,{prop:"id",label:"ID",width:"80"}),p(S,{prop:"date",label:"日期",width:"120"}),p(S,{prop:"customerName",label:"客户名称","min-width":"180"}),p(S,{prop:"productName",label:"产品名称","min-width":"150"}),p(S,{prop:"quantity",label:"数量",width:"80",align:"right"}),p(S,{prop:"unitPrice",label:"单价",width:"120",align:"right"},{default:f((({row:e})=>[T(x(formatCurrency(e.unitPrice)),1)])),_:1}),p(S,{prop:"totalAmount",label:"总金额",width:"120",align:"right"},{default:f((({row:e})=>[T(x(formatCurrency(e.totalAmount)),1)])),_:1}),p(S,{prop:"status",label:"状态",width:"100"},{default:f((({row:e})=>[p(y(J),{status:e.status,"status-map":d},null,8,["status"])])),_:1})])),_:1},8,["data","loading","total","page-size"])])),p(k,{class:"performance-card"},{header:f((()=>[s("div",ie,[a[10]||(a[10]=s("span",null,"性能指标",-1)),p(v,{type:"primary",link:"",onClick:startPerformanceTest},{default:f((()=>a[9]||(a[9]=[T(" 开始性能测试 ")]))),_:1})])])),default:f((()=>[s("div",oe,[s("div",ne,[a[11]||(a[11]=s("span",{class:"label"},"渲染时间:",-1)),s("span",se,x(c.renderTime)+"ms",1)]),s("div",re,[a[12]||(a[12]=s("span",{class:"label"},"滚动性能:",-1)),s("span",de,x(c.scrollFPS)+"FPS",1)]),s("div",ue,[a[13]||(a[13]=s("span",{class:"label"},"内存使用:",-1)),s("span",ce,x(c.memoryUsage)+"MB",1)])])])),_:1})])}}}),[["__scopeId","data-v-970203d2"]]);export{pe as default};
