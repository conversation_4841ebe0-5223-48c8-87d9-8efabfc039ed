# 报账管理微服务功能设计说明书

## 文档信息
- 文档版本：V1.0
- 编写日期：2025-04-28
- 最后修订日期：2025-04-28
- 编写人：报账系统开发团队
- 审核人：技术总监

## 目录
- [1. 概述](#1-概述)
- [2. 系统架构](#2-系统架构)
- [3. 功能模块设计](#3-功能模块设计)
- [4. 数据模型设计](#4-数据模型设计)
- [5. 接口设计](#5-接口设计)
- [6. 流程设计](#6-流程设计)
- [7. 安全设计](#7-安全设计)
- [8. 部署方案](#8-部署方案)
- [9. 附录](#9-附录)

## 1. 概述

### 1.1 设计目的
本文档旨在详细描述中小企业报账管理微服务的功能设计，为企业提供全面的费用报销和报账管理解决方案。文档面向开发团队、测试团队和项目管理人员，提供系统实现的技术指导。

### 1.2 系统定位
报账管理微服务是企业财务管理系统的重要组件，负责管理企业各类费用报销、差旅报销、预算控制、发票管理和报账审批等业务流程。本微服务采用现代化的微服务架构，可独立运行，也可与企业其他系统（如财务系统、人力资源系统等）集成，提供灵活、高效的报账管理能力，特别适合中小企业的需求。

### 1.3 设计范围
本设计说明书主要涵盖以下内容：
1. 费用报销管理
2. 差旅报销管理
3. 预算管理
4. 发票管理
5. 报销单审批流程
6. 费用统计分析
7. 预付款管理
8. 报销政策管理
9. 与其他系统的集成接口

### 1.4 术语与缩略语
- **EMS**：Expense Management System，费用管理系统
- **ERMS**：Expense Reimbursement Microservice，报账管理微服务
- **OCR**：Optical Character Recognition，光学字符识别
- **API**：Application Programming Interface，应用程序编程接口
- **REST**：Representational State Transfer，表述性状态转移
- **JWT**：JSON Web Token，用于身份验证的令牌标准
- **RBAC**：Role-Based Access Control，基于角色的访问控制
- **VAT**：Value-Added Tax，增值税

## 2. 系统架构

### 2.1 总体架构
报账管理微服务采用现代化的微服务架构，主要包括以下几个层次：

1. **表现层**：提供Web界面和移动应用接口，负责用户交互
2. **API网关层**：统一接口管理，负责请求路由、负载均衡、认证授权等
3. **业务服务层**：核心业务逻辑实现，包括报销管理、审批流程等服务
4. **数据访问层**：负责数据持久化和数据访问
5. **集成层**：与外部系统的集成接口

系统架构图如下：
```
+--------------------------------------------------+
|                   表现层                          |
|  +----------------+  +------------------------+  |
|  |    Web界面     |  |      移动应用接口      |  |
|  +----------------+  +------------------------+  |
+--------------------------------------------------+
                        |
+--------------------------------------------------+
|                   API网关层                       |
|  +----------------+  +------------------------+  |
|  |  认证与授权    |  |    请求路由与转发      |  |
|  +----------------+  +------------------------+  |
+--------------------------------------------------+
                        |
+--------------------------------------------------+
|                  业务服务层                       |
|  +----------------+  +------------------------+  |
|  |费用报销管理服务|  |     差旅报销服务       |  |
|  +----------------+  +------------------------+  |
|  +----------------+  +------------------------+  |
|  |  预算管理服务  |  |     发票管理服务       |  |
|  +----------------+  +------------------------+  |
|  +----------------+  +------------------------+  |
|  |  审批流程服务  |  |     统计分析服务       |  |
|  +----------------+  +------------------------+  |
|  +----------------+  +------------------------+  |
|  |预付款管理服务  |  |   报销政策管理服务     |  |
|  +----------------+  +------------------------+  |
+--------------------------------------------------+
                        |
+--------------------------------------------------+
|                  数据访问层                       |
|  +----------------+  +------------------------+  |
|  |  关系型数据库  |  |      缓存服务          |  |
|  +----------------+  +------------------------+  |
|  +----------------+  +------------------------+  |
|  |  文档存储      |  |      搜索引擎          |  |
|  +----------------+  +------------------------+  |
+--------------------------------------------------+
                        |
+--------------------------------------------------+
|                   集成层                          |
|  +----------------+  +------------------------+  |
|  | 财务系统集成   |  |    人力系统集成        |  |
|  +----------------+  +------------------------+  |
|  +----------------+  +------------------------+  |
|  | 银行系统集成   |  |    OCR服务集成         |  |
|  +----------------+  +------------------------+  |
+--------------------------------------------------+
```

### 2.2 技术架构
报账管理微服务采用以下技术栈：

1. **前端技术**：
   - React/Vue.js：前端框架
   - Ant Design/Element UI：UI组件库
   - Redux/Vuex：状态管理
   - Axios：HTTP客户端
   - ECharts：数据可视化

2. **后端技术**：
   - Spring Boot：应用框架
   - Spring Cloud：微服务框架
   - Spring Security：安全框架
   - MyBatis/Hibernate：ORM框架
   - Flowable/Camunda：工作流引擎
   - Redis：缓存服务

3. **数据存储**：
   - MySQL/PostgreSQL：关系型数据库
   - MongoDB：文档存储（用于存储发票图像和OCR结果）
   - Elasticsearch：全文搜索引擎
   - MinIO：对象存储（用于存储附件）

4. **DevOps工具**：
   - Docker：容器化
   - Kubernetes：容器编排
   - Jenkins：持续集成/持续部署
   - Prometheus/Grafana：监控和可视化

### 2.3 部署架构
系统采用容器化部署方式，主要包括以下环境：

1. **开发环境**：供开发人员开发和测试使用
2. **测试环境**：供测试团队进行功能测试和集成测试
3. **预生产环境**：与生产环境配置相同，用于最终验证
4. **生产环境**：正式运行环境

每个环境都包含完整的微服务组件，通过配置管理实现环境隔离。

### 2.4 集成架构
报账管理微服务可以独立运行，也可以与其他系统集成。主要的集成点包括：

1. **与财务系统的集成**：
   - 同步报销单数据，用于财务记账
   - 接收付款执行结果
   - 同步科目和成本中心信息

2. **与人力资源系统的集成**：
   - 获取员工信息，用于报销单申请和审批
   - 同步组织架构信息，用于权限控制和审批流程
   - 同步员工银行账户信息

3. **与银行系统的集成**：
   - 发送付款指令
   - 接收付款结果
   - 查询账户余额

4. **与OCR服务的集成**：
   - 发送发票图像进行识别
   - 接收识别结果
   - 验证发票的有效性

## 3. 功能模块设计

### 3.1 功能模块概览

报账管理微服务的功能模块主要围绕费用报销流程展开，包括费用报销管理、差旅报销管理、预算管理、发票管理、审批流程管理、统计分析、预付款管理和报销政策管理等核心功能。

功能模块结构图如下：
```
+--------------------------------------------------+
|             报账管理微服务                        |
+--------------------------------------------------+
                        |
        +---------------+---------------+
        |               |               |
+---------------+ +---------------+ +---------------+
|费用报销管理模块| |差旅报销管理模块| |  预算管理模块  |
+---------------+ +---------------+ +---------------+
        |               |               |
        +---------------+---------------+
                        |
        +---------------+---------------+
        |               |               |
+---------------+ +---------------+ +---------------+
|  发票管理模块  | |审批流程管理模块| |统计分析模块   |
+---------------+ +---------------+ +---------------+
        |               |               |
        +---------------+---------------+
                        |
        +---------------+---------------+
        |               |               |
+---------------+ +---------------+ +---------------+
| 预付款管理模块 | |报销政策管理模块| |系统集成模块   |
+---------------+ +---------------+ +---------------+
```

### 3.2 费用报销管理模块

费用报销管理模块负责处理员工日常费用报销的申请、审批和报销流程。

#### 3.2.1 功能列表

| 功能ID | 功能名称 | 功能描述 | 优先级 |
|--------|---------|---------|-------|
| F3.2.1 | 报销单创建 | 创建费用报销单，填写报销信息 | 高 |
| F3.2.2 | 报销单编辑 | 编辑未提交的报销单 | 高 |
| F3.2.3 | 报销单提交 | 提交报销单进入审批流程 | 高 |
| F3.2.4 | 报销单查询 | 查询报销单列表和详情 | 高 |
| F3.2.5 | 报销单导出 | 导出报销单数据为Excel或PDF | 中 |
| F3.2.6 | 报销单打印 | 打印报销单 | 中 |
| F3.2.7 | 报销单撤回 | 撤回未审批的报销单 | 中 |
| F3.2.8 | 报销单复制 | 基于已有报销单创建新报销单 | 低 |
| F3.2.9 | 批量导入 | 批量导入报销明细 | 低 |

#### 3.2.2 功能详细说明

**F3.2.1 报销单创建**

- 功能描述：创建费用报销单，填写报销信息。
- 操作流程：
  1. 用户登录系统，进入"费用报销"模块
  2. 点击"新建报销单"按钮
  3. 系统显示报销单创建表单
  4. 用户填写报销单基本信息：
     - 报销单标题：报销单的主题
     - 报销类型：普通报销、差旅报销等
     - 报销部门：报销所属部门
     - 成本中心：费用归属的成本中心
     - 报销说明：对报销的补充说明
     - 收款账户：报销款项的收款账户
  5. 用户添加报销明细：
     - 费用类型：如交通费、餐饮费、办公用品等
     - 费用日期：发生费用的日期
     - 费用金额：报销的金额
     - 费用说明：对该笔费用的说明
     - 发票信息：关联的发票信息
     - 附件：上传相关凭证
  6. 系统自动计算报销总金额
  7. 用户可以保存为草稿或直接提交
- 权限要求：所有员工
- 业务规则：
  - 报销单必须包含至少一条报销明细
  - 报销金额必须大于0
  - 报销明细必须选择正确的费用类型
  - 报销明细可以关联发票或上传凭证
  - 系统自动生成唯一的报销单号
  - 报销单可以保存为草稿状态，稍后继续编辑
- 异常处理：
  - 必填信息缺失：系统提示必填项，阻止保存
  - 金额格式错误：系统提示金额格式错误，要求修正
  - 超出预算限制：系统提示超出预算，要求确认

**F3.2.2 报销单编辑**

- 功能描述：编辑未提交的报销单。
- 操作流程：
  1. 用户登录系统，进入"费用报销"模块
  2. 在报销单列表中选择状态为"草稿"的报销单
  3. 点击"编辑"按钮
  4. 系统显示报销单编辑表单，加载已保存的报销信息
  5. 用户修改报销单信息和报销明细
  6. 用户可以保存修改或提交报销单
- 权限要求：报销单创建人
- 业务规则：
  - 只能编辑状态为"草稿"的报销单
  - 编辑后的报销单必须符合报销单创建的业务规则
  - 报销单编辑历史需要记录
- 异常处理：
  - 报销单已提交：系统提示无法编辑已提交的报销单
  - 非创建人编辑：系统提示无权限编辑他人的报销单

### 3.3 差旅报销管理模块

差旅报销管理模块负责处理员工出差相关的报销申请、审批和报销流程。

#### 3.3.1 功能列表

| 功能ID | 功能名称 | 功能描述 | 优先级 |
|--------|---------|---------|-------|
| F3.3.1 | 差旅申请创建 | 创建出差申请，填写出差计划 | 高 |
| F3.3.2 | 差旅申请审批 | 审批出差申请 | 高 |
| F3.3.3 | 差旅报销单创建 | 创建差旅报销单，填写差旅费用 | 高 |
| F3.3.4 | 差旅标准查询 | 查询差旅费用标准 | 高 |
| F3.3.5 | 差旅费用超标提醒 | 提醒用户差旅费用超出标准 | 中 |
| F3.3.6 | 差旅行程管理 | 管理出差行程信息 | 中 |
| F3.3.7 | 差旅统计分析 | 统计分析差旅费用数据 | 中 |
| F3.3.8 | 常用出差地点管理 | 管理常用出差地点 | 低 |

#### 3.3.2 功能详细说明

**F3.3.1 差旅申请创建**

- 功能描述：创建出差申请，填写出差计划。
- 操作流程：
  1. 用户登录系统，进入"差旅报销"模块
  2. 点击"新建差旅申请"按钮
  3. 系统显示差旅申请创建表单
  4. 用户填写差旅申请基本信息：
     - 申请标题：差旅申请的主题
     - 出差目的：出差的目的和任务
     - 出差类型：国内出差、国际出差等
     - 出差部门：出差所属部门
     - 成本中心：费用归属的成本中心
     - 出差说明：对出差的补充说明
  5. 用户添加出差行程：
     - 出发地：出发的城市或地点
     - 目的地：到达的城市或地点
     - 开始日期：出差开始日期
     - 结束日期：出差结束日期
     - 交通方式：飞机、火车、汽车等
     - 住宿安排：酒店、自行安排等
  6. 用户填写预算信息：
     - 交通费预算：预计的交通费用
     - 住宿费预算：预计的住宿费用
     - 餐饮费预算：预计的餐饮费用
     - 其他费用预算：预计的其他费用
  7. 系统自动计算总预算金额
  8. 用户可以保存为草稿或直接提交
- 权限要求：所有员工
- 业务规则：
  - 出差申请必须包含至少一条行程
  - 出差结束日期不能早于开始日期
  - 预算金额必须大于0
  - 系统自动生成唯一的申请单号
  - 出差申请可以保存为草稿状态，稍后继续编辑
  - 系统自动根据目的地和职级计算差旅标准
- 异常处理：
  - 必填信息缺失：系统提示必填项，阻止保存
  - 日期格式错误：系统提示日期格式错误，要求修正
  - 预算超出限制：系统提示预算超出限制，要求确认

**F3.3.3 差旅报销单创建**

- 功能描述：创建差旅报销单，填写差旅费用。
- 操作流程：
  1. 用户登录系统，进入"差旅报销"模块
  2. 点击"新建差旅报销单"按钮
  3. 系统显示差旅报销单创建表单
  4. 用户选择关联的差旅申请（可选）
  5. 系统自动加载差旅申请的基本信息和行程
  6. 用户填写报销单基本信息：
     - 报销单标题：报销单的主题
     - 报销部门：报销所属部门
     - 成本中心：费用归属的成本中心
     - 报销说明：对报销的补充说明
     - 收款账户：报销款项的收款账户
  7. 用户添加报销明细：
     - 费用类型：如交通费、住宿费、餐饮费等
     - 费用日期：发生费用的日期
     - 费用金额：报销的金额
     - 费用说明：对该笔费用的说明
     - 发票信息：关联的发票信息
     - 附件：上传相关凭证
  8. 系统自动计算报销总金额
  9. 系统自动检查费用是否符合差旅标准
  10. 用户可以保存为草稿或直接提交
- 权限要求：所有员工
- 业务规则：
  - 差旅报销单必须包含至少一条报销明细
  - 报销金额必须大于0
  - 报销明细必须选择正确的费用类型
  - 报销明细可以关联发票或上传凭证
  - 系统自动生成唯一的报销单号
  - 差旅报销单可以保存为草稿状态，稍后继续编辑
  - 系统自动检查费用是否符合差旅标准
- 异常处理：
  - 必填信息缺失：系统提示必填项，阻止保存
  - 金额格式错误：系统提示金额格式错误，要求修正
  - 超出差旅标准：系统提示超出标准，要求确认或提供说明

### 3.4 发票管理模块

发票管理模块负责管理报销过程中的发票信息，包括发票录入、OCR识别、查验和管理等功能。

#### 3.4.1 功能列表

| 功能ID | 功能名称 | 功能描述 | 优先级 |
|--------|---------|---------|-------|
| F3.4.1 | 发票录入 | 手动录入发票信息 | 高 |
| F3.4.2 | 发票OCR识别 | 通过OCR技术自动识别发票信息 | 高 |
| F3.4.3 | 发票查验 | 查验发票的真伪和有效性 | 高 |
| F3.4.4 | 发票关联 | 将发票关联到报销明细 | 高 |
| F3.4.5 | 发票查询 | 查询发票列表和详情 | 中 |
| F3.4.6 | 发票导出 | 导出发票数据 | 中 |
| F3.4.7 | 发票统计 | 统计发票数据 | 中 |
| F3.4.8 | 发票归档 | 归档发票信息 | 低 |

#### 3.4.2 功能详细说明

**F3.4.1 发票录入**

- 功能描述：手动录入发票信息。
- 操作流程：
  1. 用户登录系统，进入"发票管理"模块
  2. 点击"新增发票"按钮
  3. 系统显示发票录入表单
  4. 用户选择发票类型：增值税专用发票、增值税普通发票、电子发票等
  5. 用户填写发票信息：
     - 发票代码：发票的代码
     - 发票号码：发票的号码
     - 开票日期：发票的开具日期
     - 销售方名称：销售方的名称
     - 销售方税号：销售方的税号
     - 购买方名称：购买方的名称
     - 购买方税号：购买方的税号
     - 金额：不含税金额
     - 税额：税额
     - 价税合计：含税总金额
     - 发票内容：发票的具体内容
  6. 用户上传发票图片（可选）
  7. 用户保存发票信息
- 权限要求：所有员工
- 业务规则：
  - 发票代码和发票号码的组合必须唯一
  - 开票日期不能晚于当前日期
  - 金额、税额和价税合计必须符合计算规则
  - 发票信息可以保存为草稿状态，稍后继续编辑
- 异常处理：
  - 必填信息缺失：系统提示必填项，阻止保存
  - 发票重复：系统提示发票已存在，阻止保存
  - 金额计算错误：系统提示金额计算错误，要求修正

**F3.4.2 发票OCR识别**

- 功能描述：通过OCR技术自动识别发票信息。
- 操作流程：
  1. 用户登录系统，进入"发票管理"模块
  2. 点击"OCR识别"按钮
  3. 系统显示发票上传界面
  4. 用户上传发票图片或拍照
  5. 系统调用OCR服务识别发票信息
  6. 系统显示识别结果，包括发票类型、代码、号码、金额等信息
  7. 用户确认或修正识别结果
  8. 用户保存发票信息
- 权限要求：所有员工
- 业务规则：
  - 支持多种发票类型的识别
  - 识别结果需要用户确认
  - 识别失败的情况下，用户可以手动录入
  - 系统记录识别的准确率，用于服务优化
- 异常处理：
  - 图片格式不支持：系统提示不支持的图片格式
  - 识别失败：系统提示识别失败，建议用户手动录入
  - 识别结果不完整：系统提示识别结果不完整，要求用户补充

## 4. 数据模型设计

### 4.1 数据模型概览

报账管理微服务的数据模型主要围绕报销单、差旅申请、发票、预算等核心实体展开，通过合理的关联关系，支持报销业务的全流程管理。

数据模型概览图如下：
```
+----------------+       +----------------+       +----------------+
|    报销单      |-------|   报销明细     |-------|     发票       |
+----------------+       +----------------+       +----------------+
        |                        |                       |
        |                        |                       |
+----------------+       +----------------+       +----------------+
|   差旅申请     |-------|   差旅行程     |-------|   费用类型     |
+----------------+       +----------------+       +----------------+
        |                        |                       |
        |                        |                       |
+----------------+       +----------------+       +----------------+
|   审批流程     |-------|   审批记录     |-------|   预算管理     |
+----------------+       +----------------+       +----------------+
        |                        |                       |
        |                        |                       |
+----------------+       +----------------+       +----------------+
|   预付款       |-------|   报销政策     |-------|   成本中心     |
+----------------+       +----------------+       +----------------+
```

### 4.2 核心实体定义

#### 4.2.1 报销单实体（ExpenseReport）

报销单实体是系统的核心实体，记录报销单的基本信息和状态。

| 字段名 | 类型 | 描述 | 是否必填 |
|--------|------|------|---------|
| id | String | 报销单ID，主键 | 是 |
| reportNo | String | 报销单号，唯一 | 是 |
| title | String | 报销单标题 | 是 |
| reportType | Enum | 报销类型（普通报销/差旅报销等） | 是 |
| departmentId | String | 部门ID，外键 | 是 |
| costCenterId | String | 成本中心ID，外键 | 是 |
| applicantId | String | 申请人ID，外键 | 是 |
| totalAmount | Decimal | 报销总金额 | 是 |
| currency | String | 币种 | 是 |
| paymentAccountId | String | 收款账户ID，外键 | 是 |
| description | String | 报销说明 | 否 |
| travelRequestId | String | 关联的差旅申请ID，外键 | 否 |
| status | Enum | 报销单状态（草稿/审批中/已通过/已拒绝/已付款等） | 是 |
| processInstanceId | String | 流程实例ID | 否 |
| createdBy | String | 创建人 | 是 |
| createdTime | DateTime | 创建时间 | 是 |
| updatedBy | String | 更新人 | 是 |
| updatedTime | DateTime | 更新时间 | 是 |

#### 4.2.2 报销明细实体（ExpenseItem）

报销明细实体记录报销单的明细信息。

| 字段名 | 类型 | 描述 | 是否必填 |
|--------|------|------|---------|
| id | String | 明细ID，主键 | 是 |
| reportId | String | 报销单ID，外键 | 是 |
| expenseTypeId | String | 费用类型ID，外键 | 是 |
| expenseDate | Date | 费用发生日期 | 是 |
| amount | Decimal | 费用金额 | 是 |
| description | String | 费用说明 | 否 |
| invoiceId | String | 关联的发票ID，外键 | 否 |
| attachmentIds | String | 附件ID列表，逗号分隔 | 否 |
| createdBy | String | 创建人 | 是 |
| createdTime | DateTime | 创建时间 | 是 |
| updatedBy | String | 更新人 | 是 |
| updatedTime | DateTime | 更新时间 | 是 |

#### 4.2.3 发票实体（Invoice）

发票实体记录发票的信息。

| 字段名 | 类型 | 描述 | 是否必填 |
|--------|------|------|---------|
| id | String | 发票ID，主键 | 是 |
| invoiceType | Enum | 发票类型（增值税专用发票/增值税普通发票/电子发票等） | 是 |
| invoiceCode | String | 发票代码 | 是 |
| invoiceNo | String | 发票号码 | 是 |
| invoiceDate | Date | 开票日期 | 是 |
| sellerName | String | 销售方名称 | 是 |
| sellerTaxNo | String | 销售方税号 | 是 |
| buyerName | String | 购买方名称 | 是 |
| buyerTaxNo | String | 购买方税号 | 是 |
| amount | Decimal | 金额（不含税） | 是 |
| taxAmount | Decimal | 税额 | 是 |
| totalAmount | Decimal | 价税合计 | 是 |
| content | String | 发票内容 | 否 |
| imageUrl | String | 发票图片URL | 否 |
| verificationStatus | Enum | 查验状态（未查验/查验通过/查验不通过） | 是 |
| verificationTime | DateTime | 查验时间 | 否 |
| verificationResult | String | 查验结果 | 否 |
| status | Enum | 发票状态（未使用/已使用/已作废等） | 是 |
| createdBy | String | 创建人 | 是 |
| createdTime | DateTime | 创建时间 | 是 |
| updatedBy | String | 更新人 | 是 |
| updatedTime | DateTime | 更新时间 | 是 |

#### 4.2.4 差旅申请实体（TravelRequest）

差旅申请实体记录差旅申请的信息。

| 字段名 | 类型 | 描述 | 是否必填 |
|--------|------|------|---------|
| id | String | 申请ID，主键 | 是 |
| requestNo | String | 申请单号，唯一 | 是 |
| title | String | 申请标题 | 是 |
| travelType | Enum | 出差类型（国内出差/国际出差等） | 是 |
| departmentId | String | 部门ID，外键 | 是 |
| costCenterId | String | 成本中心ID，外键 | 是 |
| applicantId | String | 申请人ID，外键 | 是 |
| purpose | String | 出差目的 | 是 |
| startDate | Date | 出差开始日期 | 是 |
| endDate | Date | 出差结束日期 | 是 |
| totalBudget | Decimal | 总预算金额 | 是 |
| transportationBudget | Decimal | 交通费预算 | 否 |
| accommodationBudget | Decimal | 住宿费预算 | 否 |
| mealBudget | Decimal | 餐饮费预算 | 否 |
| otherBudget | Decimal | 其他费用预算 | 否 |
| description | String | 申请说明 | 否 |
| status | Enum | 申请状态（草稿/审批中/已通过/已拒绝等） | 是 |
| processInstanceId | String | 流程实例ID | 否 |
| createdBy | String | 创建人 | 是 |
| createdTime | DateTime | 创建时间 | 是 |
| updatedBy | String | 更新人 | 是 |
| updatedTime | DateTime | 更新时间 | 是 |

#### 4.2.5 审批记录实体（ApprovalRecord）

审批记录实体记录报销单和差旅申请的审批信息。

| 字段名 | 类型 | 描述 | 是否必填 |
|--------|------|------|---------|
| id | String | 记录ID，主键 | 是 |
| businessType | Enum | 业务类型（报销单/差旅申请等） | 是 |
| businessId | String | 业务ID，外键 | 是 |
| taskId | String | 任务ID | 是 |
| taskName | String | 任务名称 | 是 |
| approverId | String | 审批人ID，外键 | 是 |
| approvalAction | Enum | 审批动作（通过/拒绝/退回等） | 是 |
| comment | String | 审批意见 | 否 |
| attachmentIds | String | 附件ID列表，逗号分隔 | 否 |
| approvalTime | DateTime | 审批时间 | 是 |
| createdBy | String | 创建人 | 是 |
| createdTime | DateTime | 创建时间 | 是 |
| updatedBy | String | 更新人 | 是 |
| updatedTime | DateTime | 更新时间 | 是 |

## 5. 接口设计

### 5.1 接口概览

报账管理微服务提供RESTful API接口，供前端应用和其他系统调用。接口主要分为以下几类：

1. **报销单管理接口**：提供报销单的CRUD操作和状态管理
2. **差旅申请管理接口**：提供差旅申请的CRUD操作和状态管理
3. **发票管理接口**：提供发票的CRUD操作和OCR识别
4. **审批流程接口**：提供审批流程的操作
5. **预算管理接口**：提供预算的CRUD操作和查询
6. **统计分析接口**：提供各类统计报表和分析数据
7. **系统集成接口**：提供与其他系统集成的接口

### 5.2 接口规范

#### 5.2.1 通用规范

- 所有接口采用RESTful风格设计
- 请求和响应数据格式均为JSON
- 接口URL采用kebab-case命名方式
- 接口版本通过URL路径区分，如/api/v1/expense-reports
- 所有接口需要进行身份认证和权限校验
- 接口响应统一格式如下：

```json
{
  "code": 200,          // 状态码，200表示成功，其他表示失败
  "message": "success", // 状态描述
  "data": {             // 响应数据，可能是对象、数组或null
    // 具体数据
  },
  "timestamp": ************* // 时间戳
}
```

#### 5.2.2 分页查询规范

分页查询接口统一使用以下参数：

- page：当前页码，从1开始
- size：每页记录数
- sort：排序字段，格式为"字段名,asc|desc"，如"createdTime,desc"
- 其他查询条件作为请求参数

分页查询响应格式如下：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      // 数据列表
    ],
    "totalElements": 100,  // 总记录数
    "totalPages": 10,      // 总页数
    "size": 10,            // 每页记录数
    "number": 1,           // 当前页码
    "first": true,         // 是否第一页
    "last": false          // 是否最后一页
  },
  "timestamp": *************
}
```

### 5.3 核心接口定义

#### 5.3.1 报销单管理接口

**1. 创建报销单**

- 请求方法：POST
- 请求路径：/api/v1/expense-reports
- 请求参数：
  ```json
  {
    "title": "办公用品报销",
    "reportType": "GENERAL",
    "departmentId": "dept001",
    "costCenterId": "cc001",
    "paymentAccountId": "account001",
    "description": "购买办公用品的报销",
    "items": [
      {
        "expenseTypeId": "type001",
        "expenseDate": "2023-04-15",
        "amount": 200.00,
        "description": "购买文具",
        "invoiceId": "invoice001",
        "attachmentIds": ["attach001", "attach002"]
      },
      {
        "expenseTypeId": "type002",
        "expenseDate": "2023-04-16",
        "amount": 300.00,
        "description": "购买打印纸",
        "invoiceId": "invoice002",
        "attachmentIds": ["attach003"]
      }
    ]
  }
  ```
- 响应结果：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": "report001",
      "reportNo": "ER-********-0001",
      "title": "办公用品报销",
      "status": "DRAFT",
      "totalAmount": 500.00,
      // 其他报销单信息
    },
    "timestamp": *************
  }
  ```

**2. 提交报销单**

- 请求方法：POST
- 请求路径：/api/v1/expense-reports/{id}/submit
- 请求参数：无
- 响应结果：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": "report001",
      "reportNo": "ER-********-0001",
      "status": "APPROVING",
      "processInstanceId": "process001",
      // 其他报销单信息
    },
    "timestamp": *************
  }
  ```

#### 5.3.2 发票管理接口

**1. 发票OCR识别**

- 请求方法：POST
- 请求路径：/api/v1/invoices/ocr
- 请求参数：
  ```json
  {
    "imageBase64": "base64编码的图片数据",
    "invoiceType": "VAT_NORMAL" // 可选，指定发票类型
  }
  ```
- 响应结果：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "invoiceType": "VAT_NORMAL",
      "invoiceCode": "1234567890",
      "invoiceNo": "12345678",
      "invoiceDate": "2023-04-15",
      "sellerName": "XX供应商",
      "sellerTaxNo": "91110105MA01ABCD1X",
      "buyerName": "XX公司",
      "buyerTaxNo": "91110105MA01EFGH2X",
      "amount": 176.99,
      "taxAmount": 23.01,
      "totalAmount": 200.00,
      "content": "办公用品",
      "confidence": 0.95 // 识别置信度
    },
    "timestamp": *************
  }
  ```

**2. 发票查验**

- 请求方法：POST
- 请求路径：/api/v1/invoices/verify
- 请求参数：
  ```json
  {
    "invoiceType": "VAT_NORMAL",
    "invoiceCode": "1234567890",
    "invoiceNo": "12345678",
    "invoiceDate": "2023-04-15",
    "totalAmount": 200.00
  }
  ```
- 响应结果：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "verified": true,
      "verificationTime": *************,
      "verificationResult": "发票有效",
      "details": {
        "sellerName": "XX供应商",
        "sellerTaxNo": "91110105MA01ABCD1X",
        "buyerName": "XX公司",
        "buyerTaxNo": "91110105MA01EFGH2X",
        "amount": 176.99,
        "taxAmount": 23.01,
        "totalAmount": 200.00,
        "content": "办公用品",
        "status": "VALID"
      }
    },
    "timestamp": *************
  }
  ```

#### 5.3.3 审批流程接口

**1. 获取待办任务**

- 请求方法：GET
- 请求路径：/api/v1/approval-tasks
- 请求参数：
  - page：当前页码
  - size：每页记录数
  - businessType：业务类型（可选）
  - keyword：关键字搜索（可选）
- 响应结果：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "content": [
        {
          "taskId": "task001",
          "taskName": "部门经理审批",
          "businessType": "EXPENSE_REPORT",
          "businessId": "report001",
          "businessNo": "ER-********-0001",
          "businessTitle": "办公用品报销",
          "applicantName": "张三",
          "applicantDepartment": "研发部",
          "amount": 500.00,
          "createTime": *************,
          "dueTime": 1650086400000
        },
        // 更多任务
      ],
      "totalElements": 10,
      "totalPages": 1,
      "size": 10,
      "number": 1,
      "first": true,
      "last": true
    },
    "timestamp": *************
  }
  ```

**2. 审批任务**

- 请求方法：POST
- 请求路径：/api/v1/approval-tasks/{taskId}/approve
- 请求参数：
  ```json
  {
    "action": "APPROVE", // APPROVE, REJECT, RETURN
    "comment": "同意报销",
    "attachmentIds": [] // 可选，附件ID列表
  }
  ```
- 响应结果：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "taskId": "task001",
      "completed": true,
      "nextTasks": [
        {
          "taskId": "task002",
          "taskName": "财务审批",
          "assignee": "李四"
        }
      ]
    },
    "timestamp": *************
  }
  ```

#### 5.3.4 与财务系统的集成接口

**1. 同步付款结果**

- 请求方法：POST
- 请求路径：/api/v1/integration/finance/payment-results
- 请求参数：
  ```json
  {
    "reportId": "report001",
    "reportNo": "ER-********-0001",
    "paymentStatus": "SUCCESS",
    "paymentTime": *************,
    "paymentAmount": 500.00,
    "paymentReference": "PAY20230421001",
    "paymentMethod": "BANK_TRANSFER",
    "paymentAccount": "6222021234567890123",
    "paymentRemark": "办公用品报销付款"
  }
  ```
- 响应结果：
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "reportId": "report001",
      "reportNo": "ER-********-0001",
      "status": "PAID",
      "updateTime": *************
    },
    "timestamp": *************
  }
  ```

## 6. 流程设计

### 6.1 流程设计概述

报账管理微服务的核心业务流程包括报销单审批流程、差旅申请审批流程等。这些流程基于工作流引擎实现，采用BPMN 2.0标准进行设计。

流程设计的主要原则包括：
1. **标准化**：遵循BPMN 2.0标准，确保流程定义的规范性和可读性
2. **灵活性**：支持流程的动态调整和版本管理
3. **可视化**：提供流程的可视化展示和监控
4. **可追溯**：记录流程的执行历史和审计日志
5. **异常处理**：支持流程的异常处理和回退机制

### 6.2 报销单审批流程

#### 6.2.1 流程图

报销单审批流程的BPMN流程图如下：

![报销单审批流程](images/expense_ms_expense_report_approval_process.svg)

#### 6.2.2 流程节点说明

| 节点ID | 节点名称 | 节点类型 | 处理人 | 说明 |
|--------|---------|---------|-------|------|
| start | 流程开始 | 开始事件 | - | 流程的起点 |
| submitExpenseReport | 提交报销单 | 用户任务 | 申请人 | 填写报销单信息并提交 |
| checkBudget | 检查预算 | 服务任务 | - | 自动检查预算是否足够 |
| budgetGateway | 预算检查结果 | 排他网关 | - | 判断预算是否足够 |
| departmentManagerApproval | 部门经理审批 | 用户任务 | 部门经理 | 部门经理审批报销单 |
| approvalGateway1 | 审批结果判断 | 排他网关 | - | 判断部门经理审批结果 |
| financeApproval | 财务审批 | 用户任务 | 财务人员 | 财务人员审批报销单 |
| approvalGateway2 | 审批结果判断 | 排他网关 | - | 判断财务审批结果 |
| payment | 付款处理 | 服务任务 | - | 自动处理付款 |
| notifyApplicant | 通知申请人 | 服务任务 | - | 自动通知申请人结果 |
| end | 流程结束 | 结束事件 | - | 流程的终点 |

### 6.3 差旅申请审批流程

#### 6.3.1 流程图

差旅申请审批流程的BPMN流程图如下：

![差旅申请审批流程](images/expense_ms_travel_request_approval_process.svg)

#### 6.3.2 流程节点说明

| 节点ID | 节点名称 | 节点类型 | 处理人 | 说明 |
|--------|---------|---------|-------|------|
| start | 流程开始 | 开始事件 | - | 流程的起点 |
| submitTravelRequest | 提交差旅申请 | 用户任务 | 申请人 | 填写差旅申请信息并提交 |
| checkTravelPolicy | 检查差旅政策 | 服务任务 | - | 自动检查是否符合差旅政策 |
| policyGateway | 政策检查结果 | 排他网关 | - | 判断是否符合差旅政策 |
| departmentManagerApproval | 部门经理审批 | 用户任务 | 部门经理 | 部门经理审批差旅申请 |
| approvalGateway | 审批结果判断 | 排他网关 | - | 判断部门经理审批结果 |
| notifyApplicant | 通知申请人 | 服务任务 | - | 自动通知申请人结果 |
| end | 流程结束 | 结束事件 | - | 流程的终点 |

## 7. 安全设计

### 7.1 认证与授权

报账管理微服务采用基于JWT的认证机制和基于角色的访问控制（RBAC）模型，确保系统的安全性。

#### 7.1.1 认证机制

系统采用JWT（JSON Web Token）作为认证机制，主要流程如下：
1. 用户通过用户名和密码进行登录
2. 认证服务验证用户身份，生成包含用户信息和权限的JWT令牌
3. 客户端在后续请求中携带JWT令牌
4. 服务端验证JWT令牌的有效性和权限

#### 7.1.2 授权模型

系统采用RBAC（基于角色的访问控制）模型，主要包括以下几个方面：
1. 用户（User）：系统的使用者
2. 角色（Role）：权限的集合，如财务人员、部门经理、普通员工等
3. 权限（Permission）：对特定资源的操作权限，如查看报销单、审批报销单等
4. 资源（Resource）：系统中的各类资源，如报销单、差旅申请等

### 7.2 数据安全

#### 7.2.1 数据加密

系统对敏感数据进行加密存储，主要包括：
1. 用户密码采用BCrypt算法加盐哈希存储
2. 数据传输采用HTTPS协议加密
3. 敏感信息（如银行账号）采用AES算法加密存储

#### 7.2.2 数据访问控制

系统实现细粒度的数据访问控制，确保用户只能访问其权限范围内的数据：
1. 普通员工只能访问自己创建的报销单和差旅申请
2. 部门经理可以访问本部门的报销单和差旅申请
3. 财务人员可以访问所有报销单和差旅申请
4. 系统管理员可以访问所有数据

### 7.3 审计日志

系统记录完整的审计日志，包括：
1. 用户登录日志：记录用户的登录时间、IP地址等信息
2. 操作日志：记录用户对系统资源的操作，如创建、修改、删除等
3. 审批日志：记录报销单和差旅申请的审批过程和结果
4. 系统日志：记录系统运行状态和异常情况

## 8. 部署方案

### 8.1 部署架构

报账管理微服务采用容器化部署方式，基于Kubernetes进行容器编排和管理。部署架构如下：

```
+--------------------------------------------------+
|                Kubernetes集群                     |
|                                                  |
|  +----------------+  +------------------------+  |
|  |  Ingress控制器  |  |      API网关           |  |
|  +----------------+  +------------------------+  |
|                                                  |
|  +----------------+  +------------------------+  |
|  | 报账管理微服务  |  |     认证授权服务       |  |
|  +----------------+  +------------------------+  |
|                                                  |
|  +----------------+  +------------------------+  |
|  |  工作流服务    |  |     数据库服务         |  |
|  +----------------+  +------------------------+  |
|                                                  |
|  +----------------+  +------------------------+  |
|  |  缓存服务      |  |     消息队列服务       |  |
|  +----------------+  +------------------------+  |
|                                                  |
|  +----------------+  +------------------------+  |
|  |  监控服务      |  |     日志服务           |  |
|  +----------------+  +------------------------+  |
+--------------------------------------------------+
```

### 8.2 部署环境

系统部署环境包括以下几个环境：
1. **开发环境**：供开发人员开发和测试使用
2. **测试环境**：供测试团队进行功能测试和集成测试
3. **预生产环境**：与生产环境配置相同，用于最终验证
4. **生产环境**：正式运行环境

### 8.3 部署流程

系统采用CI/CD（持续集成/持续部署）流程，主要包括以下步骤：
1. 代码提交到版本控制系统（Git）
2. Jenkins自动触发构建和单元测试
3. 构建Docker镜像并推送到镜像仓库
4. 自动部署到开发环境并进行集成测试
5. 手动触发部署到测试环境并进行功能测试
6. 手动触发部署到预生产环境并进行验收测试
7. 手动触发部署到生产环境

## 9. 附录

### 9.1 术语表

| 术语 | 定义 |
|------|------|
| 报销单 | 员工申请报销费用的单据 |
| 差旅申请 | 员工申请出差的单据 |
| 发票 | 购买商品或服务的凭证 |
| 预算 | 部门或项目的费用限额 |
| 成本中心 | 费用归属的组织单元 |
| 审批流程 | 报销单或差旅申请的审批过程 |
| OCR | 光学字符识别，用于自动识别发票信息 |
| 费用类型 | 报销费用的分类，如交通费、餐饮费等 |

### 9.2 参考文档

1. BPMN 2.0规范
2. Spring Boot参考文档
3. Spring Cloud参考文档
4. Kubernetes用户指南
5. 《企业财务管理规范》

### 9.3 版本历史

| 版本号 | 日期 | 修改人 | 修改内容 |
|--------|------|--------|---------|
| V1.0 | 2025-04-28 | 报账系统开发团队 | 初始版本 |