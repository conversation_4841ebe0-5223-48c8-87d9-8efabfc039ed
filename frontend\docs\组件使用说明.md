# 组件使用说明

## DataTable 组件

`DataTable`是一个基于Element Plus的表格组件封装，提供了常用的表格功能，包括数据展示、分页、排序、选择等。

### 引入方式

```typescript
import { DataTable } from "@/components";
```

### 基本用法

```vue
<DataTable
  :data="tableData"
  :loading="loading"
  :total="total"
  v-model:current-page="queryParams.page"
  v-model:page-size="queryParams.size"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
>
  <el-table-column prop="name" label="名称" />
  <el-table-column prop="date" label="日期" />
  
  <!-- 自定义列 -->
  <el-table-column label="状态">
    <template #default="{ row }">
      <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
        {{ row.status === 'active' ? '启用' : '禁用' }}
      </el-tag>
    </template>
  </el-table-column>
  
  <!-- 操作列 -->
  <template #action="{ row }">
    <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
    <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
  </template>
</DataTable>
```

### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| data | Array | [] | 表格数据 |
| border | Boolean | true | 是否显示边框 |
| stripe | Boolean | true | 是否显示斑马纹 |
| height | String/Number | "" | 表格高度 |
| maxHeight | String/Number | "" | 表格最大高度 |
| rowKey | String | "id" | 行数据的键值 |
| loading | Boolean | false | 加载状态 |
| showSelection | Boolean | false | 是否显示选择列 |
| showIndex | Boolean | false | 是否显示序号列 |
| indexLabel | String | "序号" | 序号列标签 |
| indexWidth | String/Number | 60 | 序号列宽度 |
| indexAlign | String | "center" | 序号列对齐方式 |
| actionLabel | String | "操作" | 操作列标签 |
| actionWidth | String/Number | 150 | 操作列宽度 |
| actionFixed | String | "right" | 操作列是否固定 |
| actionAlign | String | "center" | 操作列对齐方式 |
| showPagination | Boolean | true | 是否显示分页 |
| currentPage | Number | 1 | 当前页码 |
| pageSize | Number | 10 | 每页条数 |
| pageSizes | Array | [10, 20, 50, 100] | 每页条数选项 |
| paginationLayout | String | "total, sizes, prev, pager, next, jumper" | 分页布局 |
| total | Number | 0 | 总条数 |
| background | Boolean | true | 分页按钮是否带有背景色 |

### 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| selection-change | 选择项发生变化时触发 | selection: 已选择的行数据数组 |
| size-change | 每页显示条数改变时触发 | size: 新的每页条数 |
| current-change | 当前页码改变时触发 | page: 新的页码 |
| update:currentPage | 当前页码改变时触发（用于v-model绑定） | page: 新的页码 |
| update:pageSize | 每页条数改变时触发（用于v-model绑定） | size: 新的每页条数 |

### 插槽

| 插槽名 | 说明 | 作用域参数 |
|-------|------|-----------|
| default | 表格列的内容 | - |
| toolbar | 表格顶部工具栏 | - |
| action | 操作列的内容 | row: 行数据, index: 行索引 |

### 方法

通过ref可以获取到DataTable实例并调用以下方法：

| 方法名 | 说明 | 参数 |
|-------|------|------|
| clearSelection | 清空选择 | - |
| toggleRowSelection | 切换行选中状态 | row: 行数据, selected: 是否选中 |
| toggleAllSelection | 切换全选状态 | - |

### 使用示例

```vue
<template>
  <div class="page-container">
    <DataTable
      ref="tableRef"
      :data="tableData"
      :loading="loading"
      :total="total"
      v-model:current-page="queryParams.page"
      v-model:page-size="queryParams.size"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      showSelection
      showIndex
    >
      <!-- 工具栏 -->
      <template #toolbar>
        <div class="toolbar-left">
          <el-button type="primary" @click="handleCreate">新增</el-button>
          <el-button type="danger" @click="handleBatchDelete" :disabled="!selectedRows.length">批量删除</el-button>
        </div>
      </template>
      
      <!-- 表格列 -->
      <el-table-column prop="name" label="名称" min-width="120" />
      <el-table-column prop="date" label="日期" width="120" />
      
      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="handleView(row)">查看</el-button>
        <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
      </template>
    </DataTable>
  </div>
</template>
```

## SearchForm 组件

`SearchForm`是一个基于Element Plus的表单组件封装，专门用于搜索场景，提供了搜索、重置等功能。

### 引入方式

```typescript
import { SearchForm } from "@/components";
```

### 基本用法

```vue
<SearchForm
  v-model="queryParams"
  :loading="loading"
  @search="handleSearch"
  @reset="resetQuery"
>
  <el-form-item label="用户名" prop="username">
    <el-input v-model="queryParams.username" placeholder="请输入用户名" clearable />
  </el-form-item>
  
  <el-form-item label="状态" prop="status">
    <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
      <el-option label="启用" value="active" />
      <el-option label="禁用" value="inactive" />
    </el-select>
  </el-form-item>
</SearchForm>
```

### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| modelValue | Object | {} | 表单数据对象（通过v-model绑定） |
| loading | Boolean | false | 加载状态，影响搜索按钮的禁用状态 |
| labelWidth | String | "80px" | 表单项标签宽度 |
| inline | Boolean | true | 是否行内表单 |
| showResetButton | Boolean | true | 是否显示重置按钮 |
| resetButtonText | String | "重置" | 重置按钮文本 |
| showSearchButton | Boolean | true | 是否显示搜索按钮 |
| searchButtonText | String | "搜索" | 搜索按钮文本 |
| showExpand | Boolean | false | 是否显示展开/收起按钮 |
| expandButtonText | String | "展开" | 展开按钮文本 |
| collapseButtonText | String | "收起" | 收起按钮文本 |
| defaultExpand | Boolean | false | 默认是否展开 |
| maxRow | Number | 1 | 默认显示的最大行数 |

### 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| search | 点击搜索按钮时触发 | formData: 表单数据对象 |
| reset | 点击重置按钮时触发 | - |
| update:modelValue | 表单数据变化时触发（用于v-model绑定） | formData: 表单数据对象 |

### 插槽

| 插槽名 | 说明 | 作用域参数 |
|-------|------|-----------|
| default | 表单项内容 | - |
| actions | 自定义操作按钮区域 | - |

## 组件组合使用

`SearchForm`和`DataTable`组件通常一起使用，构成一个完整的列表页面。以下是一个典型的列表页面结构：

```vue
<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <PageHeader title="用户管理">
      <template #actions>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>新增用户
        </el-button>
      </template>
    </PageHeader>

    <!-- 搜索表单 -->
    <SearchForm
      v-model="queryParams"
      :loading="loading"
      @search="handleSearch"
      @reset="resetQuery"
    >
      <!-- 搜索表单项 -->
    </SearchForm>

    <!-- 数据表格 -->
    <DataTable
      :data="tableData"
      :loading="loading"
      :total="total"
      v-model:current-page="queryParams.page"
      v-model:page-size="queryParams.size"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      showSelection
      showIndex
    >
      <!-- 表格列 -->
      
      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="handleView(row)">查看</el-button>
        <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
      </template>
    </DataTable>
  </div>
</template>
```

## 最佳实践

1. **使用v-model绑定分页参数**：使用`v-model:current-page`和`v-model:page-size`绑定分页参数，可以简化分页逻辑。

2. **合理设置表格高度**：根据页面布局设置表格的`height`或`max-height`属性，可以使表格在固定高度内滚动，提升用户体验。

3. **使用插槽自定义内容**：充分利用组件提供的插槽，自定义表格列内容和操作按钮。

4. **处理空数据和加载状态**：始终处理好空数据和加载状态，提供良好的用户反馈。

5. **组合使用组件**：将`SearchForm`、`DataTable`和`PageHeader`等组件组合使用，构建完整的页面。
