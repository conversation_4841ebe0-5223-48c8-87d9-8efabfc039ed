import {
  extendChartView,
  extendComponentModel,
  extendComponentView,
  extendSeriesModel,
  format_exports,
  graphic_exports,
  helper_exports,
  number_exports,
  time_exports,
  util_exports as util_exports2
} from "./chunk-ZNLTD4KQ.js";
import {
  install as install3,
  install10 as install13,
  install11 as install14,
  install12 as install16,
  install13 as install17,
  install14 as install18,
  install15 as install19,
  install16 as install20,
  install17 as install21,
  install18 as install22,
  install19 as install23,
  install2 as install4,
  install20 as install24,
  install21 as install25,
  install22 as install26,
  install3 as install5,
  install4 as install6,
  install5 as install7,
  install6 as install9,
  install7 as install10,
  install8 as install11,
  install9 as install12
} from "./chunk-HIACVI4Y.js";
import "./chunk-N4SBZIBW.js";
import {
  install,
  install2
} from "./chunk-XVRKI5XB.js";
import {
  install as install27,
  install10 as install36,
  install11 as install37,
  install12 as install38,
  install13 as install39,
  install14 as install40,
  install17 as install41,
  install18 as install42,
  install19 as install43,
  install2 as install28,
  install20 as install44,
  install21 as install45,
  install22 as install46,
  install23 as install47,
  install24 as install48,
  install25 as install49,
  install26 as install50,
  install3 as install29,
  install4 as install30,
  install5 as install31,
  install6 as install32,
  install7 as install33,
  install8 as install34,
  install9 as install35
} from "./chunk-FUPZXNWA.js";
import {
  install3 as install8,
  install4 as install15
} from "./chunk-6VITRHS4.js";
import {
  Axis_default,
  Chart_default,
  Component_default as Component_default2,
  PRIORITY,
  SeriesData_default,
  connect,
  dataTool,
  dependencies,
  disConnect,
  disconnect,
  dispose,
  getCoordinateSystemDimensions,
  getInstanceByDom,
  getInstanceById,
  getMap,
  init,
  parseGeoJSON,
  registerAction,
  registerCoordinateSystem,
  registerLayout,
  registerLoading,
  registerMap,
  registerPostInit,
  registerPostUpdate,
  registerPreprocessor,
  registerProcessor,
  registerTheme,
  registerTransform,
  registerUpdateLifecycle,
  registerVisual,
  setCanvasCreator,
  throttle,
  use,
  version
} from "./chunk-345VRSDE.js";
import {
  brushSingle,
  zrender_exports
} from "./chunk-LBWVJU7O.js";
import {
  installUniversalTransition
} from "./chunk-DLA5P77Z.js";
import {
  installLabelLayout
} from "./chunk-NSE7IMS3.js";
import "./chunk-JM3IW6WR.js";
import {
  Component_default,
  Model_default,
  Series_default,
  registerLocale
} from "./chunk-KWRFNUMR.js";
import {
  color_exports,
  env_default,
  matrix_exports,
  setPlatformAPI,
  util_exports,
  vector_exports
} from "./chunk-VLPSD4XZ.js";
import "./chunk-G3PMV62Z.js";

// node_modules/echarts/index.js
use([install2]);
use([install]);
use([install3, install4, install5, install6, install7, install9, install10, install11, install12, install13, install14, install16, install17, install18, install19, install20, install21, install22, install23, install24, install25, install26]);
use(install28);
use(install29);
use(install8);
use(install30);
use(install15);
use(install31);
use(install32);
use(install33);
use(install34);
use(install27);
use(install35);
use(install36);
use(install37);
use(install38);
use(install39);
use(install40);
use(install41);
use(install44);
use(install42);
use(install43);
use(install47);
use(install45);
use(install46);
use(install48);
use(install49);
use(install50);
use(installUniversalTransition);
use(installLabelLayout);
export {
  Axis_default as Axis,
  Chart_default as ChartView,
  Component_default as ComponentModel,
  Component_default2 as ComponentView,
  SeriesData_default as List,
  Model_default as Model,
  PRIORITY,
  Series_default as SeriesModel,
  color_exports as color,
  connect,
  dataTool,
  dependencies,
  disConnect,
  disconnect,
  dispose,
  env_default as env,
  extendChartView,
  extendComponentModel,
  extendComponentView,
  extendSeriesModel,
  format_exports as format,
  getCoordinateSystemDimensions,
  getInstanceByDom,
  getInstanceById,
  getMap,
  graphic_exports as graphic,
  helper_exports as helper,
  init,
  brushSingle as innerDrawElementOnCanvas,
  matrix_exports as matrix,
  number_exports as number,
  parseGeoJSON,
  parseGeoJSON as parseGeoJson,
  registerAction,
  registerCoordinateSystem,
  registerLayout,
  registerLoading,
  registerLocale,
  registerMap,
  registerPostInit,
  registerPostUpdate,
  registerPreprocessor,
  registerProcessor,
  registerTheme,
  registerTransform,
  registerUpdateLifecycle,
  registerVisual,
  setCanvasCreator,
  setPlatformAPI,
  throttle,
  time_exports as time,
  use,
  util_exports2 as util,
  vector_exports as vector,
  version,
  util_exports as zrUtil,
  zrender_exports as zrender
};
//# sourceMappingURL=echarts.js.map
