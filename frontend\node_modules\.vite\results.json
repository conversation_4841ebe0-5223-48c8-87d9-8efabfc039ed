{"version": "3.1.4", "results": [[":tests/performance/database-performance.spec.ts", {"duration": 0, "failed": false}], [":tests/performance/load-test.spec.ts", {"duration": 0, "failed": false}], [":tests/components/ResponsiveContainer.spec.ts", {"duration": 15.864400000000387, "failed": true}], [":tests/performance/render-performance.spec.ts", {"duration": 153.89689999999973, "failed": true}], [":tests/security/PermissionTest.spec.ts", {"duration": 8.851399999999558, "failed": false}], [":tests/performance/api-performance.spec.ts", {"duration": 1084.1849999999995, "failed": true}], [":tests/components/LoginForm.spec.ts", {"duration": 0, "failed": true}], [":tests/unit/reports/reports.spec.ts", {"duration": 0, "failed": true}], [":tests/api.reports.spec.ts", {"duration": 0, "failed": true}], [":tests/e2e/login.spec.ts", {"duration": 0, "failed": true}], [":tests/components/report/ReportChart.spec.ts", {"duration": 47.23989999999867, "failed": false}], [":tests/excel.spec.ts", {"duration": 3.9089999999996508, "failed": false}], [":tests/components/report/ExcelExport.spec.ts", {"duration": 7.2478000000000975, "failed": false}], [":tests/components/HelloWorld.spec.ts", {"duration": 0, "failed": true}], [":tests/unit/example.spec.ts", {"duration": 146.65399999999954, "failed": false}]]}