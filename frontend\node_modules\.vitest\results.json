{"version": "1.3.1", "results": [[":tests/performance/render-performance.spec.ts", {"duration": 124, "failed": true}], [":tests/components/ResponsiveContainer.spec.ts", {"duration": 14, "failed": true}], [":tests/unit/reports/reports.spec.ts", {"duration": 0, "failed": true}], [":tests/api.reports.spec.ts", {"duration": 0, "failed": true}], [":tests/components/report/ReportChart.spec.ts", {"duration": 62, "failed": false}], [":tests/performance/load-test.spec.ts", {"duration": 0, "failed": false}], [":tests/performance/database-performance.spec.ts", {"duration": 0, "failed": false}], [":tests/security/PermissionTest.spec.ts", {"duration": 22, "failed": false}], [":tests/performance/api-performance.spec.ts", {"duration": 1052, "failed": true}], [":tests/components/LoginForm.spec.ts", {"duration": 0, "failed": true}], [":tests/excel.spec.ts", {"duration": 5, "failed": false}], [":tests/components/HelloWorld.spec.ts", {"duration": 0, "failed": true}], [":tests/components/report/ExcelExport.spec.ts", {"duration": 9, "failed": false}], [":tests/unit/example.spec.ts", {"duration": 146, "failed": false}], [":tests/e2e/login.spec.ts", {"duration": 0, "failed": true}]]}