import {
  createRouter,
  createWebHistory,
  createWebHashHistory,
} from "vue-router";
import Layout from "../layout/index.vue";
import { useSettingsStore } from "../store/settings";

// 静态路由
export const constantRoutes = [
  {
    path: "/login",
    name: "Login",
    component: () =>
      import(/* webpackChunkName: "login" */ "../views/login/index.vue"),
    meta: { title: "登录", public: true, hidden: true },
  },
  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    meta: { title: "首页" },
    children: [
      {
        path: "dashboard",
        name: "Dashboard",
        component: () =>
          import(
            /* webpackChunkName: "dashboard" */ "../views/dashboard/index.vue"
          ),
        meta: { title: "首页", icon: "Odometer" },
      },
    ],
  },
  {
    path: "/user",
    component: Layout,
    name: "User",
    redirect: "/user/list",
    meta: { title: "用户管理" },
    children: [
      {
        path: "list",
        name: "UserList",
        component: () =>
          import(/* webpackChunkName: "user" */ "../views/user/list.vue"),
        meta: { title: "用户列表", icon: "User" },
      },
      {
        path: "create",
        name: "CreateUser",
        component: () =>
          import(/* webpackChunkName: "user" */ "../views/user/create.vue"),
        meta: { title: "新增用户", icon: "Plus", hidden: true },
      },
      {
        path: "detail/:id",
        name: "UserDetail",
        component: () =>
          import(/* webpackChunkName: "user" */ "../views/user/detail.vue"),
        meta: { title: "用户详情", icon: "InfoFilled", hidden: true },
      },
      {
        path: "edit/:id",
        name: "EditUser",
        component: () =>
          import(/* webpackChunkName: "user" */ "../views/user/edit.vue"),
        meta: { title: "编辑用户", icon: "Edit", hidden: true },
      },
    ],
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/404",
    meta: { hidden: true },
  },
  {
    path: "/404",
    component: () =>
      import(/* webpackChunkName: "error" */ "../views/error-page/404.vue"),
    meta: { title: "404", hidden: true, public: true },
  },
];

// 检查组件是否存在
// 此函数暂未使用，但保留以备后续需要
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const checkComponentExists = async (
  component: () => Promise<any>
): Promise<boolean> => {
  try {
    await component();
    return true;
  } catch (error) {
    console.error("组件加载失败:", error);
    return false;
  }
};

// 创建路由器实例
const router = createRouter({
  history: createWebHashHistory(
    import.meta.env.VITE_APP_ENV === "development"
      ? ""
      : import.meta.env.VITE_BUILD_BASE
  ),
  routes: constantRoutes,
  linkActiveClass: "router-link-active",
  linkExactActiveClass: "router-link-exact-active",
  scrollBehavior: () => ({ top: 0 }),
});

// 全局前置守卫 - 增强版本
// eslint-disable-next-line @typescript-eslint/no-unused-vars
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title
    ? `${to.meta.title} - ${import.meta.env.VITE_APP_TITLE}`
    : import.meta.env.VITE_APP_TITLE;

  // 登录权限控制
  const token = localStorage.getItem("token");

  // 获取设置存储
  const settingsStore = useSettingsStore();
  const isDeveloperMode = settingsStore.settings.developerMode;

  // 检查是否需要开发者模式
  if (to.meta.requireDeveloperMode && !isDeveloperMode) {
    console.warn("该页面需要开发者模式，但当前未启用开发者模式");
    next("/dashboard");
    return;
  }

  // 检查是否需要管理员权限
  if (to.meta.requireAdmin) {
    // 从用户信息中获取角色ID
    const userInfoStr = localStorage.getItem("user_info");
    let isAdmin = false;

    if (userInfoStr) {
      try {
        const userInfo = JSON.parse(userInfoStr);
        // 角色ID 1和2分别对应超级管理员和管理员
        isAdmin = userInfo.role_id === 1 || userInfo.role_id === 2;
      } catch (e) {
        console.error("解析用户信息失败", e);
      }
    }

    if (!isAdmin) {
      console.warn("该页面需要管理员权限，但当前用户不是管理员");
      next("/dashboard");
      return;
    }
  }

  // 调试信息
  console.log("当前路由:", to.path);
  console.log("目标路由完整信息:", to);
  console.log("是否公开页面:", !!to.meta.public);
  console.log("是否有token:", !!token);
  console.log("是否开发者模式:", isDeveloperMode);

  if (to.path === "/login") {
    // 如果访问的是登录页
    if (token) {
      // 已登录则跳转到首页
      console.log("已登录，重定向到首页");
      next("/dashboard");
    } else {
      // 未登录则正常进入登录页
      console.log("未登录，正常进入登录页");
      next();
    }
  } else if (to.matched.length === 0) {
    // 处理未匹配到路由的情况
    console.log("路由未匹配，重定向到首页");
    next("/dashboard");
  } else {
    // 访问其他页面时
    if (to.meta.public) {
      // 公开页面，直接放行
      console.log("访问公开页面，直接放行");
      next();
    } else {
      // 非公开页面需要验证登录
      if (!token) {
        // 未登录，重定向到登录页
        console.log("未登录，重定向到登录页");
        next("/login");
      } else {
        // 已登录，判断组件是否存在
        try {
          // 确保路由组件存在且能够加载
          if (
            to.matched.some(
              (record) => !record.components || !record.components.default
            )
          ) {
            console.warn("路由组件不存在:", to.path);
            next("/dashboard");
            return;
          }

          // 正常放行
          console.log("已登录，正常放行");
          next();
        } catch (error) {
          console.error("路由导航错误:", error);
          next("/dashboard");
        }
      }
    }
  }
});

export default router;
